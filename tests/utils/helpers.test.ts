import {
  makeBackendRequest,
  extractFields,
  formatDateTime,
  convertYYMMDDhhmmssToISO,
  convertISOToYYMMDDhhmmss,
  getDecimals,
  formatAmount,
  getCurrency
} from '../../src/utils/helpers';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('Helper Functions', () => {
  describe('extractFields', () => {
    it('should extract specified fields', () => {
      const data = {
        field1: 'value1',
        field2: 'value2',
        field3: 'value3',
        field4: 'value4'
      };

      const result = extractFields(data, {
        fields: ['field1', 'field3']
      });

      expect(result).toEqual({
        field1: 'value1',
        field3: 'value3'
      });
    });

    it('should return all fields when includeAll is true', () => {
      const data = {
        field1: 'value1',
        field2: 'value2'
      };

      const result = extractFields(data, {
        fields: ['field1'],
        includeAll: true
      });

      expect(result).toEqual(data);
    });

    it('should handle non-existent fields', () => {
      const data = {
        field1: 'value1',
        field2: 'value2'
      };

      const result = extractFields(data, {
        fields: ['field1', 'field3']
      });

      expect(result).toEqual({
        field1: 'value1'
      });
    });
  });

  describe('formatDateTime', () => {
    it('should format ISO date strings correctly', () => {
      expect(formatDateTime('2023-05-15T14:30:00Z')).toBe('15.05.2023 14:30');
      expect(formatDateTime('2023-01-01T01:05:00Z')).toBe('01.01.2023 01:05');
      expect(formatDateTime('2023-12-31T23:59:00Z')).toBe('31.12.2023 23:59');
    });
  });

  describe('convertYYMMDDhhmmssToISO', () => {
    it('should convert valid YYMMDDhhmmss strings to ISO format', () => {
      // June 22, 2024, 12:06:39
      const result = convertYYMMDDhhmmssToISO('240622120639');
      expect(result).toMatch(/^2024-06-22T12:06:39/);
    });

    it('should return null for invalid format', () => {
      expect(convertYYMMDDhhmmssToISO('2406221206')).toBeNull(); // Too short
      expect(convertYYMMDDhhmmssToISO('24062212063X')).toBeNull(); // Non-numeric
      expect(convertYYMMDDhhmmssToISO(undefined)).toBeNull(); // Undefined
    });

    it('should handle edge cases', () => {
      // January 1, 2020, 00:00:00
      const result1 = convertYYMMDDhhmmssToISO('200101000000');
      expect(result1).toMatch(/^2020-01-01T00:00:00/);

      // December 31, 2029, 23:59:59
      const result2 = convertYYMMDDhhmmssToISO('291231235959');
      expect(result2).toMatch(/^2029-12-31T23:59:59/);
    });
  });

  describe('convertISOToYYMMDDhhmmss', () => {
    it('should convert ISO format to YYMMDDhhmmss', () => {
      expect(convertISOToYYMMDDhhmmss('2024-06-22T12:06:39Z')).toBe('240622120639');
      expect(convertISOToYYMMDDhhmmss('2020-01-01T00:00:00Z')).toBe('200101000000');
      expect(convertISOToYYMMDDhhmmss('2029-12-31T23:59:59Z')).toBe('291231235959');
    });

    it('should return null for invalid input', () => {
      expect(convertISOToYYMMDDhhmmss('invalid-date')).toBeNull();
      expect(convertISOToYYMMDDhhmmss(undefined)).toBeNull();
    });
  });

  describe('getDecimals', () => {
    it('should return correct decimal places for currencies', () => {
      expect(getDecimals('EUR')).toBe(2);
      expect(getDecimals('392')).toBe(0); // JPY numeric code
      expect(getDecimals('048')).toBe(3); // BHD numeric code
    });

    it('should return default value for unknown currencies', () => {
      expect(getDecimals('XYZ')).toBe(2); // Default should be 2
    });
  });

  describe('formatAmount', () => {
    it('should format amounts with correct decimal places', () => {
      expect(formatAmount(123.45, 'EUR')).toBe('000000012345');
      expect(formatAmount(123, '392')).toBe('000000000123'); // JPY with 0 decimals
      expect(formatAmount(123.456, '048')).toBe('000000123456'); // BHD with 3 decimals
    });

    it('should handle large amounts', () => {
      expect(formatAmount(1234567890123, 'EUR')).toBe('999999999999'); // Should cap at max allowed
    });
  });

  describe('getCurrency', () => {
    it('should return mapped currency code', () => {
      // Assuming currencyMap has '978': 'EUR'
      expect(getCurrency('978')).toBe('EUR');
    });

    it('should return original code if not in map', () => {
      expect(getCurrency('ABC')).toBe('ABC');
    });

    it('should handle undefined input', () => {
      expect(getCurrency(undefined)).toBeUndefined();
    });
  });
});
