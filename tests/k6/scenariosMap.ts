export const scenariosMap = {
  stressTest: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '30s', target: 50 },
      { duration: '30s', target: 100 },
      { duration: '30s', target: 200 },
      { duration: '30s', target: 300 },
      { duration: '30s', target: 400 },
      { duration: '30s', target: 0 }
    ],
    exec: 'runTest'
  },

  loadTest: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '10s', target: 50 },
      { duration: '1m', target: 50 },
      { duration: '10s', target: 0 }
    ],
    exec: 'runTest'
  },

  spikeTest: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '10s', target: 10 },
      { duration: '10s', target: 100 },
      { duration: '10s', target: 10 },
      { duration: '10s', target: 100 },
      { duration: '10s', target: 0 }
    ],
    exec: 'runTest'
  },

  soakTest: {
    executor: 'constant-vus',
    vus: 50,
    duration: '30m',
    exec: 'runTest'
  },

  smokeTest: {
    executor: 'constant-vus',
    vus: 5,
    duration: '10s',
    exec: 'runTest'
  },

  breakpointTest: {
    executor: 'ramping-vus',
    startVUs: 0,
    stages: [
      { duration: '1m', target: 50 },
      { duration: '1m', target: 100 },
      { duration: '1m', target: 150 },
      { duration: '1m', target: 200 },
      { duration: '1m', target: 250 },
      { duration: '1m', target: 300 }
    ],
    exec: 'runTest'
  }
};
