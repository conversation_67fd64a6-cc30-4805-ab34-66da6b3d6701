import http from 'k6/http';
import { check, sleep } from 'k6';
import { scenariosMap } from '../scenariosMap.ts';

// SCENARIO=loadTest k6 run tests/k6/ITCard/cycle.test.ts

const scenarioToRun = __ENV.SCENARIO;
if (!scenarioToRun || !scenariosMap[scenarioToRun]) {
  throw new Error(`Invalid or missing SCENARIO env variable. Expected one of: ${Object.keys(scenariosMap).join(', ')}`);
}

export const options = {
  scenarios: {
    [scenarioToRun]: scenariosMap[scenarioToRun]
  },
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.05']
  }
};

const url = '';
// Block amount
const bodyA = {};
// Reverse amount
const bodyB = {};

export function runTest() {
  const loop = `${__VU}${Date.now()}`; // used for retrievalReferenceNumber in body
  // bodyA.retrievalReferenceNumber = loop;
  // bodyB.retrievalReferenceNumber = loop;

  // Request 1 — Body A to block amount
  const payloadA = JSON.stringify(bodyA);

  const resA = http.post(`${url}/api/itcard/transaction-authorization-messages`, payloadA, {
    headers: { 'Content-Type': 'application/json' }
  });

  check(resA, {
    'First request is 200': (r) => r.status === 200
  });

  // Request 2 — Body B to reverse amount
  const payloadB = JSON.stringify(bodyB);

  const resB = http.post(`${url}/api/itcard/transaction-authorization-messages`, payloadB, {
    headers: {
      'Content-Type': 'application/json'
    }
  });

  check(resB, {
    'Second request is 200': (r) => r.status === 200
  });

  sleep(1); // Simulate think time between requests
}
