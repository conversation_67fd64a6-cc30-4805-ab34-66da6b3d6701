import http from 'k6/http';
import { check, sleep } from 'k6';
import { scenariosMap } from '../scenariosMap.ts';

// SCENARIO=loadTest k6 run tests/k6/ITCard/requestMessage.test.ts

const scenarioToRun = __ENV.SCENARIO;
if (!scenarioToRun || !scenariosMap[scenarioToRun]) {
  throw new Error(`Invalid or missing SCENARIO env variable. Expected one of: ${Object.keys(scenariosMap).join(', ')}`);
}

export const options = {
  scenarios: {
    [scenarioToRun]: scenariosMap[scenarioToRun]
  },
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.05']
  }
};

const url = '';
// Can be used for block, reverse, advice or heartbeat, depending on the body!
const body = {
  '0': '1804',
  '7': '0425133011',
  '11': '811886',
  '12': '250425153011',
  '24': '831'
};

export function runTest() {
  const loop = `${__VU}${Date.now()}`; // used for retrievalReferenceNumber in body
  // body.retrievalReferenceNumber = loop;

  const payload = JSON.stringify(body);

  const headers = {
    'Content-Type': 'application/json'
  };

  const res = http.post(`${url}/api/itcard/transaction-authorization-messages`, payload, { headers });

  check(res, {
    'status is 200': (r) => r.status === 200,
    'body is not empty': (r) => r.body !== null && typeof r.body === 'string' && r.body.length > 0
  });

  sleep(1); // Simulate user wait time
}
