import http from 'k6/http';
import { check, sleep } from 'k6';
import { scenariosMap } from '../scenariosMap.ts';

// SCENARIO=loadTest k6 run tests/k6/CIP/txFee.test.ts

const scenarioToRun = __ENV.SCENARIO;
if (!scenarioToRun || !scenariosMap[scenarioToRun]) {
  throw new Error(`Invalid or missing SCENARIO env variable. Expected one of: ${Object.keys(scenariosMap).join(', ')}`);
}

export const options = {
  scenarios: {
    [scenarioToRun]: scenariosMap[scenarioToRun]
  },
  thresholds: {
    http_req_duration: ['p(95)<1000'],
    http_req_failed: ['rate<0.05']
  }
};

const url = '';
const token = '';
const body = {};

export function runTest() {
  const loop = `${__VU}${Date.now()}`; // used for retrievalReferenceNumber in body
  // body.retrievalReferenceNumber = loop;

  const payload = JSON.stringify(body);

  const res = http.post(`${url}/api/cip/fee-transaction`, payload, {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`
    }
  });

  check(res, {
    'First request is 200': (r) => r.status === 200
  });

  sleep(1); // Simulate think time between requests
}
