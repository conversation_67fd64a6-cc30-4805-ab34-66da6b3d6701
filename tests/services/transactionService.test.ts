import {
  isValidAuthCode,
  parseVisaNetAmount,
  parseVisaAmount,
  parseVisaConversionRate,
  processVisaAmountsWithValidation,
  normalizeCurrency,
  isSameCurrencyAndSufficient,
  convertStringFieldsToNamedInterface,
  generateValidAuthCode,
  generateField54
} from '../../src/services/transactionService';
import { codeCurrencyMap, currencyMap } from '../../src/constants/currency';
import { TransactionInterfaceNumbers } from '../../src/interfaces/transactionInterface';
import * as MongoTransactions from '../../src/queries/MongoTransactions';

// Mock the MongoTransactions module
jest.mock('../../src/queries/MongoTransactions', () => ({
  getTransactionByApprovalCode: jest.fn().mockResolvedValue(undefined),
  getTransactionByBlockId: jest.fn(),
  handleTransactionResultMessageUpdate: jest.fn(),
  updateTransactionBlockId: jest.fn()
}));

describe('Transaction Service', () => {
  describe('isValidAuthCode', () => {
    it('should validate correct auth codes', () => {
      expect(isValidAuthCode('ABC123')).toBe(true);
      expect(isValidAuthCode('123ABC')).toBe(true);
      expect(isValidAuthCode('A1B2C3')).toBe(true);
    });

    it('should reject invalid auth codes', () => {
      expect(isValidAuthCode('000000')).toBe(false); // All zeros
      expect(isValidAuthCode('00000X')).toBe(false); // 5 zeros + X
      expect(isValidAuthCode('SVCABC')).toBe(false); // Starts with SVC
      expect(isValidAuthCode('ABCDEX')).toBe(false); // Ends with X
      expect(isValidAuthCode('ABCDE ')).toBe(false); // Ends with space
      expect(isValidAuthCode('0000N')).toBe(false); // 4 zeros + N
      expect(isValidAuthCode('      ')).toBe(false); // All spaces
      expect(isValidAuthCode('AB123')).toBe(false); // Too short
      expect(isValidAuthCode('AB123XY')).toBe(false); // Too long
      expect(isValidAuthCode('ab123c')).toBe(false); // Lowercase
    });
  });

  describe('parseVisaNetAmount', () => {
    it('should parse visa amount correctly', () => {
      expect(parseVisaNetAmount('************', '978')).toBe(123.45); // EUR
      expect(parseVisaNetAmount('************', '840')).toBe(123.45); // USD
      expect(parseVisaNetAmount('************', '392')).toBe(12345); // JPY (0 decimals)
    });

    it('should add to base amount if provided', () => {
      expect(parseVisaNetAmount('************', '978', 100)).toBe(200);
    });

    it('should throw error for invalid format', () => {
      expect(() => parseVisaNetAmount('12345', '978')).toThrow('Invalid VisaNet amount format');
      expect(() => parseVisaNetAmount('abcdefghijkl', '978')).toThrow('Invalid VisaNet amount format');
    });
  });

  describe('parseVisaAmount', () => {
    it('should return undefined for undefined amount', () => {
      expect(parseVisaAmount(undefined, '978')).toBeUndefined();
    });

    it('should return raw amount if currency is undefined', () => {
      expect(parseVisaAmount('************', undefined)).toBe('************');
    });

    it('should parse valid amounts', () => {
      expect(parseVisaAmount('************', '978')).toBe(123.45);
    });

    it('should throw error for invalid format', () => {
      expect(() => parseVisaAmount('12345', '978')).toThrow('Invalid VisaNet amount format');
    });
  });

  describe('parseVisaConversionRate', () => {
    it('should parse conversion rates correctly', () => {
      expect(parseVisaConversionRate('40123456')).toBe(12.3456); // 0123456 with 4 decimal places => 12.3456
      expect(parseVisaConversionRate('20123456')).toBe(1234.56); // 0123456 with 2 decimal places => 1234.56
      expect(parseVisaConversionRate('60123456')).toBe(0.123456); // 0123456 with 6 decimal places => 0.123456
    });

    it('should throw error for invalid format', () => {
      expect(() => parseVisaConversionRate('1234')).toThrow('Invalid Field 10 format. Must be exactly 8 digits.');
      expect(() => parseVisaConversionRate('abcdefgh')).toThrow('Invalid Field 10 format. Must be exactly 8 digits.');
    });
  });

  describe('processVisaAmountsWithValidation', () => {
    it('should process transaction amount only', () => {
      const result = processVisaAmountsWithValidation({
        '4': '************',
        '49': '978'
      });

      expect(result.transactionAmount).toBe(123.45);
      expect(result.notes).toContain('Billing data (Field 6/51) not present — skipping billing validation.');
    });

    it('should validate same currency transaction', () => {
      const result = processVisaAmountsWithValidation({
        '4': '************',
        '6': '************',
        '49': '978',
        '51': '978'
      });

      expect(result.transactionAmount).toBe(123.45);
      expect(result.billingAmount).toBe(123.45);
      expect(result.isValidConversion).toBe(true);
      expect(result.notes).toContain('Same currency — conversion rate check skipped.');
    });

    it('should validate cross-currency transaction', () => {
      const result = processVisaAmountsWithValidation({
        '4': '************', // 100.00 EUR
        '6': '000000011500', // 115.00 USD
        '10': '61150000', // Rate 1.15
        '49': '978', // EUR
        '51': '840' // USD
      });

      expect(result.transactionAmount).toBe(100);
      expect(result.billingAmount).toBe(115);
      expect(result.conversionRate).toBe(1.15);
      expect(result.expectedBilling).toBe(115);
      expect(result.isValidConversion).toBe(true);
    });

    it('should detect invalid cross-currency conversion', () => {
      const result = processVisaAmountsWithValidation({
        '4': '************', // 100.00 EUR
        '6': '000000012000', // 120.00 USD
        '10': '61150000', // Rate 1.15
        '49': '978', // EUR
        '51': '840' // USD
      });

      expect(result.transactionAmount).toBe(100);
      expect(result.billingAmount).toBe(120);
      expect(result.conversionRate).toBe(1.15);
      expect(result.expectedBilling).toBe(115);
      expect(result.isValidConversion).toBe(false);
      expect(result.notes).toContain('Mismatch: expected 115, got 120');
    });

    it('should handle missing required fields', () => {
      const result = processVisaAmountsWithValidation({
        '4': '************'
      });

      expect(result.notes).toContain('Missing Field 49 (Transaction Currency)');
      expect(result.transactionAmount).toBeUndefined();
    });
  });

  describe('normalizeCurrency', () => {
    it('should return 3-letter currency codes as is', () => {
      expect(normalizeCurrency('EUR')).toBe('EUR');
      expect(normalizeCurrency('USD')).toBe('USD');
    });

    it('should convert numeric codes to 3-letter codes', () => {
      expect(normalizeCurrency('978')).toBe(currencyMap['978']);
      expect(normalizeCurrency('840')).toBe(currencyMap['840']);
    });
  });

  describe('isSameCurrencyAndSufficient', () => {
    it('should validate same currency with sufficient balance', () => {
      const result = isSameCurrencyAndSufficient({ amount: 100, currency: 'EUR' }, { amount: 50, currency: 'EUR' });
      expect(result.isValid).toBe(true);
    });

    it('should reject different currencies', () => {
      const result = isSameCurrencyAndSufficient({ amount: 100, currency: 'EUR' }, { amount: 50, currency: 'USD' });
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Currency mismatch');
    });

    it('should reject insufficient balance', () => {
      const result = isSameCurrencyAndSufficient({ amount: 50, currency: 'EUR' }, { amount: 100, currency: 'EUR' });
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Insufficient balance');
    });

    it('should handle numeric currency codes', () => {
      const result = isSameCurrencyAndSufficient({ amount: 100, currency: '978' }, { amount: 50, currency: 'EUR' });
      expect(result.isValid).toBe(true);
    });

    it('should reject undefined amounts', () => {
      const result = isSameCurrencyAndSufficient(
        { amount: undefined, currency: 'EUR' },
        { amount: 50, currency: 'EUR' }
      );
      expect(result.isValid).toBe(false);
      expect(result.message).toBe('Invalid amount values');
    });
  });

  describe('convertStringFieldsToNamedInterface', () => {
    it('should convert numeric fields to named fields', () => {
      const input = {
        '0': '0100',
        '2': '****************',
        '3': '000000',
        '4': '************',
        '49': '978',
        '11': '123456',
        '41': 'TERM1234',
        '43': 'MERCHANT NAME'
      };

      const result = convertStringFieldsToNamedInterface(input as TransactionInterfaceNumbers);

      expect(result.messageTypeIndicator).toBe('0100');
      expect(result.primaryAccountNumber).toBe('****************');
      expect(result.processingCode).toBe('000000');
      expect(result.transactionAmount).toBe(100);
      expect(result.transactionCurrencyCode).toBe('EUR');
      expect(result.stan).toBe('123456');
      expect(result.cardAcceptorTerminalId).toBe('TERM1234');
      expect(result.cardAcceptorNameLocation).toBe('MERCHANT NAME');
    });

    it('should handle a comprehensive set of fields', () => {
      const input = {
        '0': '0200',
        '2': '****************',
        '3': '000000',
        '4': '************',
        '5': '************',
        '6': '************',
        '7': '**********',
        '9': '********',
        '10': '********',
        '11': '123456',
        '12': '194500',
        '14': '2412',
        '15': '0621',
        '18': '5411',
        '22': '051',
        '32': 'ACQUIRER123',
        '37': 'REF123456789',
        '41': 'TERM5678',
        '42': 'MERCH12345678901',
        '43': 'MERCHANT NAME AND LOCATION',
        '49': '978',
        '50': '978',
        '51': '840',
        '102': 'IBAN12345678901234',
        '124': { '09': 'CARD123' }
      };

      const result = convertStringFieldsToNamedInterface(input as TransactionInterfaceNumbers);

      expect(result.messageTypeIndicator).toBe('0200');
      expect(result.primaryAccountNumber).toBe('****************');
      expect(result.processingCode).toBe('000000');
      expect(result.transactionAmount).toBe(500);
      expect(result.reconciliationAmount).toBe(500);
      expect(result.cardholderBillingAmount).toBe(550);
      expect(result.transmissionDateTime).toBe('**********');
      expect(result.reconciliationConversionRate).toBe('********');
      expect(result.cardholderBillingConversionRate).toBe('********');
      expect(result.stan).toBe('123456');
      expect(result.localTransactionTime).toBe('194500');
      expect(result.expirationDate).toBe('2412');
      expect(result.reconciliationDate).toBe('0621');
      expect(result.merchantCategoryCode).toBe('5411');
      expect(result.pointOfServiceDataCode).toBe('051');
      expect(result.acquiringInstitutionId).toBe('ACQUIRER123');
      expect(result.retrievalReferenceNumber).toBe('REF123456789');
      expect(result.cardAcceptorTerminalId).toBe('TERM5678');
      expect(result.cardAcceptorIdCode).toBe('MERCH12345678901');
      expect(result.cardAcceptorNameLocation).toBe('MERCHANT NAME AND LOCATION');
      expect(result.transactionCurrencyCode).toBe('EUR');
      expect(result.reconciliationCurrencyCode).toBe('EUR');
      expect(result.cardholderBillingCurrencyCode).toBe('USD');
      expect(result.accountIdentification1).toBe('IBAN12345678901234');
      expect(result.cardId).toBe('CARD123');
    });

    it('should handle missing optional fields', () => {
      const input = {
        '0': '0100',
        '2': '****************',
        '3': '000000',
        '4': '************',
        '49': '978',
        '32': 'ACQUIRER123'
      };

      const result = convertStringFieldsToNamedInterface(input as TransactionInterfaceNumbers);

      expect(result.messageTypeIndicator).toBe('0100');
      expect(result.primaryAccountNumber).toBe('****************');
      expect(result.processingCode).toBe('000000');
      expect(result.transactionAmount).toBe(100);
      expect(result.transactionCurrencyCode).toBe('EUR');
      expect(result.acquiringInstitutionId).toBe('ACQUIRER123');
      expect(result.stan).toBeUndefined();
      expect(result.cardAcceptorTerminalId).toBeUndefined();
      expect(result.cardAcceptorNameLocation).toBeUndefined();
    });

    it('should correctly parse amount fields with different currencies', () => {
      const input = {
        '0': '0200',
        '4': '************', // 123.45 EUR
        '6': '************', // 150.00 USD
        '49': '978', // EUR
        '51': '840', // USD
        '32': 'ACQUIRER123'
      };

      const result = convertStringFieldsToNamedInterface(input as TransactionInterfaceNumbers);

      expect(result.transactionAmount).toBe(123.45);
      expect(result.cardholderBillingAmount).toBe(150);
      expect(result.transactionCurrencyCode).toBe('EUR');
      expect(result.cardholderBillingCurrencyCode).toBe('USD');
    });
  });

  describe('generateValidAuthCode', () => {
    beforeEach(() => {
      // Reset the mock before each test
      jest.clearAllMocks();
      // Set default mock implementation to return undefined
      (MongoTransactions.getTransactionByApprovalCode as jest.Mock).mockResolvedValue(undefined);
    });

    it('should generate a valid auth code', async () => {
      const retrievalReferenceNumber = 'REF123456789';
      const IBAN = 'IBAN12345678901234';
      const transmissionDateTime = '**********'; // MMDDhhmmss format

      const authCode = await generateValidAuthCode(retrievalReferenceNumber, IBAN, transmissionDateTime);

      expect(authCode).not.toBeNull();
      expect(typeof authCode).toBe('string');
      expect(authCode?.length).toBe(6);
      expect(isValidAuthCode(authCode as string)).toBe(true);
      expect(MongoTransactions.getTransactionByApprovalCode).toHaveBeenCalled();
    });

    it('should generate different auth codes for different inputs', async () => {
      const code1 = await generateValidAuthCode('REF123456789', 'IBAN12345678901234', '**********');
      const code2 = await generateValidAuthCode('REF987654321', 'IBAN98765432109876', '**********');
      const code3 = await generateValidAuthCode('REF123456789', 'IBAN12345678901234', '0621194501');

      expect(code1).not.toBe(code2);
      expect(code1).not.toBe(code3);
    });

    it('should not generate auth codes that start with SVC', async () => {
      // We need to run this multiple times to increase the chance of catching a potential SVC prefix
      for (let i = 0; i < 10; i++) {
        const authCode = await generateValidAuthCode(
          `REF${Math.random().toString(36).substring(2, 10)}`,
          `IBAN${Math.random().toString(36).substring(2, 10)}`,
          '**********'
        );

        expect(authCode).not.toBeNull();
        expect(authCode?.startsWith('SVC')).toBe(false);
      }
    });

    it('should not generate auth codes with all zeros', async () => {
      // Run multiple times to increase chance of catching potential all-zero codes
      for (let i = 0; i < 10; i++) {
        const authCode = await generateValidAuthCode(
          `REF${Math.random().toString(36).substring(2, 10)}`,
          `IBAN${Math.random().toString(36).substring(2, 10)}`,
          '**********'
        );

        expect(authCode).not.toBeNull();
        expect(authCode).not.toBe('000000');
      }
    });

    it('should try alternative codes if a duplicate is found', async () => {
      // First call returns a transaction (indicating duplicate)
      // Second call returns undefined (indicating no duplicate)
      (MongoTransactions.getTransactionByApprovalCode as jest.Mock)
        .mockResolvedValueOnce({ _id: 'existing-transaction' })
        .mockResolvedValueOnce(undefined);

      const authCode = await generateValidAuthCode('REF123456789', 'IBAN12345678901234', '**********');

      expect(authCode).not.toBeNull();
      expect(MongoTransactions.getTransactionByApprovalCode).toHaveBeenCalledTimes(2);
    });

    it('should return null after max attempts if all codes are duplicates', async () => {
      // Always return a transaction (indicating all codes are duplicates)
      (MongoTransactions.getTransactionByApprovalCode as jest.Mock).mockResolvedValue({ _id: 'existing-transaction' });

      const authCode = await generateValidAuthCode('REF123456789', 'IBAN12345678901234', '**********');

      expect(authCode).toBeNull();
      expect(MongoTransactions.getTransactionByApprovalCode).toHaveBeenCalledTimes(8); // Max attempts
    });
  });

  describe('generateField54', () => {
    it('should generate field54 for a valid EUR transaction', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '200000', // Processing code (default account type: '20')
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '978' // EUR
      };

      const balanceResult = {
        opCurrency: 'EUR',
        op_availability: 500.0
      };

      const field54 = generateField54(body, 'C', balanceResult);

      // '20' account type + '01' amount type + '978' currency + 'C' + 12-digit amount
      const currencyCode = codeCurrencyMap['EUR'];
      expect(field54).toMatch(new RegExp(`^2001${currencyCode}C\\d{12}$`));
      expect(field54.length).toBe(20);
    });

    it('should handle different currencies (USD)', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '600000', // Processing code for 'Credit tx'
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '840'
      };

      const balanceResult = {
        opCurrency: 'USD',
        op_availability: 250.5
      };

      const field54 = generateField54(body, 'C', balanceResult);

      const numericCurrency = codeCurrencyMap['USD'];
      expect(field54).toMatch(new RegExp(`^6001${numericCurrency}C\\d{12}$`));
      expect(field54.length).toBe(20);
      expect(field54).toContain('840C');
    });

    it('should throw error for invalid currency', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '200000', // Valid format for 'Credit tx'
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '978'
      };

      const balanceResult = {
        opCurrency: 'INVALID',
        op_availability: 500.0
      };

      expect(() => generateField54(body, 'C', balanceResult)).toThrow('Invalid currency: INVALID');
    });

    it('should format large amounts correctly (cap to ************)', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '200000', // Processing code for 'Credit tx'
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '978'
      };

      const balanceResult = {
        opCurrency: 'EUR',
        op_availability: 1_000_000_000_000 // Too big → must be capped
      };

      const field54 = generateField54(body, 'C', balanceResult);
      const numericCurrency = codeCurrencyMap['EUR'];
      expect(field54).toMatch(new RegExp(`^2001${numericCurrency}C\\d{12}$`));
      expect(field54.length).toBe(20);
      const amountPart = field54.split('C')[1];
      expect(amountPart).toBe('************');
    });

    it('should correctly use debit sign for debit transactions', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '100000', // Processing code (default account type: '10')
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '978'
      };

      const balanceResult = {
        opCurrency: 'EUR',
        op_availability: 155.5
      };

      const field54 = generateField54(body, 'D', balanceResult);
      const currency = codeCurrencyMap['EUR'];
      expect(field54).toMatch(new RegExp(`^1001${currency}D\\d{12}$`)); // Debit sign
      expect(field54.length).toBe(20);
    });

    it('should correctly use debit sign for negative balances', () => {
      const body: TransactionInterfaceNumbers = {
        '0': '1200',
        '3': '000000',
        '4': '************',
        '32': 'ACQUIRER123',
        '49': '978'
      };

      const balanceResult = {
        opCurrency: 'EUR',
        op_availability: -125.5
      };

      const field54 = generateField54(body, 'D', balanceResult);
      const currency = codeCurrencyMap['EUR'];
      expect(field54).toMatch(new RegExp(`^0001${currency}D\\d{12}$`)); // Debit sign
      expect(field54.length).toBe(20);
    });
  });
});
