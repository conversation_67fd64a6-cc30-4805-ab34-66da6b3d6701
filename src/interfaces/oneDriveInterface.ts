export interface TokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  scope: string;
}

export interface OneDriveFile {
  id: string;
  name: string;
  size: number;
  '@microsoft.graph.downloadUrl': string;
  file?: {
    mimeType: string;
  };
}

export interface OneDriveFileResult {
  filename: string;
  content: Buffer;
  base64Content: string;
  mimeType: string;
  size: number;
}

export interface OneDriveUploadResult {
  id: string;
  name: string;
  size: number;
  webUrl: string;
  downloadUrl: string;
  mimeType: string;
  error?: string;
}

export interface OneDriveUploadOptions {
  fileKey: string;
  originalFilename: string;
  filename: string;
  content: Buffer | string;
  mimeType?: string;
  id: string; // identifier for the file can be _id, submissionId, etc.
  folderName: string;
}
