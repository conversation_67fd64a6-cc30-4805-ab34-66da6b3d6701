/**
 * Interface representing the data we send to OraSys for making a transaction
 */
export interface TransactionSettlementOrasysRecord {
  // uniqueId: number; // Уникален номер // we handle this in the query!
  transDate?: Date; // Дата и час на транзакция
  schDate?: Date; // Счетоводна дата
  valueDate?: Date; // Вальор
  transType: string; // Тип транзакция
  descriptor: string; // Име на търговец/описание (descriptor)
  mcc: string; // MCC код на търговец
  region: string; // Регион
  authCode: string; // Авторизационен код
  cardNo: string; // Карта (маскиран номер)
  accountNo: string; // Сметка
  cardHolder: string; // Картодържател
  transAmount: number; // Сума във валута на транзакция
  transCodval: string; // Валута на транзакция
  cardAmount: number; // Сума във валута на сметка
  cardCodval: string; // Валута на сметка
  setlAmount: number; // Сума във валута на сетълмент
  setlCodval: string; // Валута на сетълмент
  taxAmount: number; // Такса за транзакция във валута на сметка
  taxAmountFx: number; // Такса за превалутиране (Fx fee)
  idBlock?: number; // Номер на блокировка
  idRowSch: number | null; // не се попълва от IL
}

/**
 * Interface representing Settlement data we expect in the XML file we received from IT Card for each TX
 */
export interface VisSettlementTransaction {
  VIS000_MTI: string;
  VIS002_PAN: string;
  VIS003_PROC_CODE: string;
  VIS004_TRANS_AMT: string;
  VIS005_RECON_AMT: string;
  VIS006_CARDH_BILL_AMT: string;
  VIS007_TRANSMISS_DATE: string;
  VIS009_CONV_RATE_RECON: string;
  VIS010_CONV_RATE_CARDH: string;
  VIS012_TRANS_DATE: string;
  VIS014_EXP_DATE: string;
  VIS015_STTL_DATE: string;
  VIS021_TRANS_LIFE_CYC_IND: string;
  VIS022_POS_DATA_CODE: string;
  VIS023_CARD_SEQ_NBR: string;
  VIS024_FUNCTION_CODE: string;
  VIS025_MSG_REASON_CODE: string;
  VIS026_MCC: string;
  VIS027_POS_CAPABILITY: string;
  VIS030_ORIGINAL_AMT: string;
  VIS031_ARN: string;
  VIS032_ACQ_INST_ID: string;
  VIS033_FORW_INST_ID: string;
  VIS037_RRN: string;
  VIS038_APPROVAL_CODE: string;
  VIS040_SERVICE_CODE: string;
  VIS041_CARD_ACCP_TERM_ID: string;
  VIS042_CARD_ACCP_ID: string;
  VIS043_CARD_ACCP_NAME_LOC: string;
  VIS046_FEES_AMT: string;
  VIS048_ADD_DATA_PRIV: string;
  VIS054_ADD_AMOUNTS: string;
  VIS055_ICC_DATA: string;
  VIS062_ADD_DATA_PRIV2: string;
  VIS071_MESSAGE_NUMBER: string;
  VIS072_DATA_RECORD: string;
  VIS073_DATE_ACTION: string;
  VIS093_DEST_INST_ID: string;
  VIS094_ORG_INST_ID: string;
  VIS095_CARD_ISS_REF: string;
  VIS100_RECV_INST_ID: string;
  VIS104_TRANS_SPEC_DATA: string;
  VIS105_CLAIM_ID: string;
}
