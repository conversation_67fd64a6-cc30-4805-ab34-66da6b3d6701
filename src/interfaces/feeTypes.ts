import {
  CardFeeCurrency,
  CardFeeLocation,
  CardFeeTrigger,
  CardFeeType,
  CustomerType,
  FXRatesCurrency,
  OperationType,
  ProductType
} from '../constants/fees';

/**
 * Interface representing a card fee structure
 */
export interface CardFee {
  /** The account name in OraSys. We keep it only for information purposes */
  GLAccountName: string;

  /** The Transaction Type Code that will be sent to OraSys */
  feeCode: string;

  /** Short description of what the fee is for */
  description: string;

  /** Operation time for the fee, e.g., Card Issuing Fee */
  operationType: OperationType; // DE 3

  /** Type of the customer */
  customerType: CustomerType;

  /** Description of the product type */
  productType: ProductType;

  /** Location where the fee is applicable */
  location: CardFeeLocation;

  /** Modifier for the fee, e.g. courier for delivery */
  modifier: string;

  /** What will trigger the fee */
  trigger: CardFeeTrigger;

  /** Flag indicating if this fee needs to be applied to all cards based on criteria */
  loopOnCard: boolean;

  /** The descriptor that will be displayed in OraSys */
  displayDescriptor: string;

  /** Most likely like the trigger but more generalized */
  criteria: string;

  /** The type of fee (e.g. Transactional/Non-Transactional) */
  type: CardFeeType;

  /** The currency of the fee */
  currency: CardFeeCurrency;

  /** The amount of the fee */
  fixedFee: number;

  /** The percentage of the fee over the settlement amount */
  percentageFee: number;

  /** Flag indicating if the fee supports FX fee */
  supportsFXFee: boolean;
}

/**
 * Interface representing a FX rate
 */
export interface FXRate {
  /** The amount of the fee */
  flatFee: number;
  /** The percentage of the fee over the settlement amount */
  percentage: number;
  /** The currency of the transaction */
  currency: FXRatesCurrency;
  /** The client ID */
  clientID: string;
}
