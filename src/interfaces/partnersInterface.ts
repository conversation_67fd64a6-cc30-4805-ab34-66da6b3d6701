import { JwtPayload } from 'jsonwebtoken';
import { Document } from 'mongoose';

/**
 * Interface representing a partner in the database
 */
export interface partnersInterface extends Document {
  url: string;
  name: string;
  publicKey: string;
  webhookApplicantUrl?: string;
}

/**
 * Interface representing the partner custom JWT payload
 */
export interface CustomJwtPayload extends JwtPayload {
  exp?: number;
  iss?: string;
  sub?: string;
}
