import { CustomerType } from '../constants/fees';

/**
 * Interface representing the debit account in the database
 */
export interface AccountInterface extends Document {
  owners: {
    clientCode: string;
    relationship: string;
    mainOwner: boolean;
    _id: string;
  }[];
  customerType: CustomerType;
  onboarding: string;
  accountNumber: string;
  accountHolder: string;
  currencyCode: string;
  currency: string;
  relationship: string;
  bankName: string;
  status: string;
  clientCode: string;
  extAppId: string;
  productCode: string;
  productDesc: string;
  bankNumber: string;
  openDate: string;
  balance: number;
  _id: string;
  __v: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface representing the debit card in the database
 */
export interface CardInterface extends Document {
  cardHash: string;
  cardKey: string;
  expDate: string;
  status: string;
  statusCode: string;
  kind: string;
  productCode: string;
  productDesc: string;
  main: boolean;
  holder: string;
  accNo: string;
  embossName1: string;
  cardMask: string;
  createdAt?: string;
  updatedAt?: string;
}
