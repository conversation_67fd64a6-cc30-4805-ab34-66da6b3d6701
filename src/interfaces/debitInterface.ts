import { CustomerType } from '../constants/fees';

/**
 * Enum for account status
 */
export enum AccountStatus {
  ACTIVE = 'ACTIVE',
  LOCKED = 'LOCKED',
  CLOSED = 'CLOSED'
}

/**
 * Enum for card status
 */
export enum CardStatus {
  ORDERED = 'ORDERED',
  ACTIVE = 'ACTIVE',
  BLOCKED = 'BLOCKED',
  INACTIVE = 'INACTIVE',
  CLOSED = 'CLOSED'
}

/**
 * Enum for card kind/type
 */
export enum CardKind {
  DEBIT = 'DEBIT',
  CREDIT = 'CREDIT',
  PREPAID = 'PREPAID'
}

/**
 * Interface representing the debit account in the database
 */
export interface AccountInterface extends Document {
  owners: {
    clientCode: string;
    relationship: string;
    mainOwner: boolean;
    _id: string;
  }[];
  customerType: CustomerType;
  onboarding: string;
  accountNumber: string;
  accountHolder: string;
  currencyCode: string;
  currency: string;
  relationship: string;
  bankName: string;
  status: AccountStatus;
  clientCode: string;
  extAppId?: string;
  productCode: string;
  productDesc: string;
  bankNumber: string;
  openDate?: string;
  currencyName: string;
  balance: number;
  _id: string;
  __v: number;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface representing the debit card in the database
 */
export interface CardInterface extends Document {
  cardHash: string;
  cardKey: string;
  expDate: string;
  status: CardStatus;
  statusCode: string;
  kind: CardKind;
  productCode: string;
  productDesc: string;
  main: boolean;
  holder: string;
  accNo: string;
  embossName1: string;
  cardMask: string;
  createdAt?: string;
  updatedAt?: string;
}
