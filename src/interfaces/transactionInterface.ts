import { CardFeeLocation, CustomerType, OperationType, ProductType } from '../constants/fees';
import { TransactionTypeData } from '../constants/transactionType';

/**
 * Interface representing a transaction in the database
 */
export interface TransactionInterfaceData extends TransactionInterfaceNames {
  blockId?: number;
  source: TransactionSourceEnum;
  initialBodyData: any;
  messages: string[];
  processingOutcome: {
    approved: boolean;
    responseSend: any;
    txDataSendToOraSys: any;
    blockDataSendToOraSys: any;
  };
  reversal: {
    reversed: boolean;
    reversalData: any;
    responseSend: any;
  };
  settlement: {
    settled: boolean;
    settlementData: any;
    dataSendToOraSys: any;
  };
  resolvent: {
    type: TransactionResolventTypeEnum;
    resolved: boolean;
    date: string;
  };
  /** Transaction Status */
  transStatus: TransactionStatusEnum;
  /** Transaction Type */
  transType: TransactionTypeData;
  fees: {
    /** Fee Type */
    taxType?: string;
    /** Tax Amount Applied Based on Our Fee Service */
    taxAmount?: number;
    /** Tax Amount Fx Fee Used for Currency Conversion */
    taxAmountFx?: number;
    /** Fee Narrative - text which to be shown to the end customer */
    taxNarrative?: string;
  };
}

// ____________  enums  ____________

export enum TransactionSourceEnum {
  ITCard = 'IT Card',
  CIP = 'CIP',
  Unknown = 'Unknown'
}

export enum TransactionResolventTypeEnum {
  Denied = 'denied', // Denied by us or error hit the TX
  Reversal = 'reversal', // For 1420/1421
  ReversalCron = 'reversal cron', // Cronjob for unblocking
  Settlement = 'settlement', // Received Settlement file
  SettlementReject = 'settlement reject', // Received Settlement Rejected file
  Credit = 'credit', // For 29 OCT
  Inquiry = 'inquiry', // Check, do not make into a transaction
  Advice = 'advice', // For 1120/1121, 1220/1221, declined by IT Card
  ZeroAmount = 'zero amount', // Not sent to OraSys 0 amount/fee
  FeeTransaction = 'fee transaction', // CIP Insert Transaction with applying fee
  DirectTransaction = 'direct transaction', // CIP Insert Transaction with direct fee
  Unknown = 'unknown'
}

export enum TransactionStatusEnum {
  Pending = 'pending', // Accepted, approved, waiting for settlement
  Declined = 'declined', // Declined by us
  Completed = 'completed', // Settled or a direct transaction
  Reverted = 'reverted' // Reversed by IT card or CronJob
}

/**
 * Interface representing a transaction data fields from numbers to names from ITCard
 */
export interface TransactionInterfaceNames {
  /** Message Type Indicator - 4-digit field defining the version, class, function of the message and transaction initiator */
  messageTypeIndicator: string; // DE 0 (Note: While not a field, it's crucial, and often treated as field 0)
  /** Primary Account Number (PAN) */
  primaryAccountNumber?: string; // DE 2
  /** Processing Code */
  processingCode?: any; // DE 3
  /** Amount, Transaction */
  transactionAmount?: number | string; // DE 4
  /** Amount, Reconciliation */
  reconciliationAmount?: number | string; // DE 5
  /** Amount, Cardholder Billing */
  cardholderBillingAmount?: number | string; // DE 6
  /** Date and Time, Transmission */
  transmissionDateTime?: string; // DE 7
  /** Conversion Rate, Reconciliation */
  reconciliationConversionRate?: string; // DE 9
  /** Conversion Rate, Cardholder Billing */
  cardholderBillingConversionRate?: string; // DE 10
  /** Systems Trace Audit Number */
  stan?: string; // DE 11
  /** Date and Time, Local Transaction */
  localTransactionTime?: string; // DE 12
  /** Date, Expiration */
  expirationDate?: string; // DE 14
  /** Date, Reconciliation */
  reconciliationDate?: string; // DE 15
  /** Date, Conversion */
  conversionDate?: string; // DE 16
  /** Merchant Category Code - MCC */
  merchantCategoryCode?: string; // DE 18
  /** Country Code, Acquiring Institution */
  acquiringInstitutionCountryCode?: string; // DE 19
  /** Point of Service Data Code */
  pointOfServiceDataCode?: any; // DE 22
  /** Card Sequence Number */
  cardSequenceNumber?: string; // DE 23
  /** Function Code */
  functionCode?: string; // DE 24
  /** Message Reason Code */
  messageReasonCode?: string; // DE 25
  /** Card Acceptor Business Code */
  cardAcceptorBusinessCode?: string; // DE 26
  /** Amounts, Original */
  amountsOriginal?: string; // DE 30
  /** Acquirer Institution ID Code */
  acquiringInstitutionId?: string; // DE 32
  /** Forwarding Institution ID Code */
  forwardingInstitutionId?: string; // DE 33
  /** Acceptance Environment Data (TLV Format) */
  acceptanceEnvironmentData?: string; // DE 34
  /** Track 2 Data */
  track2Data?: string; // DE 35
  /** Track 3 Data */
  track3Data?: string; // DE 36
  /** Retrieval Reference Number */
  retrievalReferenceNumber?: string; // DE 37
  /** Approval Code */
  approvalCode?: string; // DE 38
  /** Action Code */
  actionCode?: string; // DE 39 (Often called "Response Code")
  /** Card Acceptor Terminal Identification */
  cardAcceptorTerminalId?: string; // DE 41
  /** Card Acceptor Identification Code */
  cardAcceptorIdCode?: string; // DE 42
  /** Card Acceptor Name/Location */
  cardAcceptorNameLocation?: any; // DE 43
  /** Additional Response Data */
  additionalResponseData?: string; // DE 44
  /** Track 1 Data */
  track1Data?: string; // DE 45
  /** Amounts, Fees - contains fee (commissions) concerning txs, keep to follow ISO format */
  amountsFeesISO?: string; // DE 46
  /** Currency Code, Transaction */
  transactionCurrencyCode?: string; // DE 49
  /** Currency Code, Reconciliation */
  reconciliationCurrencyCode?: string; // DE 50
  /** Currency Code, Cardholder Billing */
  cardholderBillingCurrencyCode?: string; // DE 51
  /** Personal Identification Number (PIN) Data */
  pinData?: string; // DE 52
  /** Amounts, Additional */
  amountsAdditional?: string; // DE 54
  /** Integrated Circuit Card System Related Data */
  iccData?: string; // DE 55
  /** Original Data Elements */
  originalDataElements?: any; // DE 56
  /** Authorization Life Cycle Code */
  authorizationLifeCycleCode?: string; // DE 57
  /** Authorizing Agent Institution ID Code */
  authorizingAgentInstitutionIdCode?: string; // DE 58
  /** Primary Reserved Private */
  reservedPrivate?: any; // DE 62
  /** Data Record */
  dataRecord?: string; // DE 72
  /** Transaction Destination Institution Id Code */
  transactionDestinationInstitutionIdCode?: string; // DE 93
  /** Transaction Destination Originator Id Code */
  transactionDestinationOriginatorIdCode?: string; // DE 94
  /** Account Identification 1 */
  accountIdentification1?: string; // DE 102
  /** Reserved for Private Use */
  reservedForPrivateUse?: any; // DE 123
  /** Card ID Information */
  cardId?: any; // DE 124
}

// ____________ ------------- IT Card ------------- ____________

/**
 * Interface representing a transaction data body from IT Card
 */
export interface TransactionInterfaceNumbers {
  /** Message Type Indicator - 4-digit field defining the version, class, function of the message and transaction initiator */
  0: string; //  AN 4

  /** Primary Account Number (PAN) */
  2?: string; //  LLVAR up to 19

  /** Processing Code */
  3?: any; //  AN 6 or object with subfields

  /** Amount, Transaction */
  4?: string; //  N 12

  /** Amount, Reconciliation */
  5?: string; //  N 12

  /** Amount, Cardholder Billing */
  6?: string; //  N 12

  /** Date and Time, Transmission */
  7?: string; //  MMDDhhmmss (N 10)

  /** Conversion Rate, Reconciliation */
  9?: string; //  N 8

  /** Conversion Rate, Cardholder Billing */
  10?: string; //  N 8

  /** Systems Trace Audit Number */
  11?: string; //  N 6

  /** Date and Time, Local Transaction */
  12?: string; //  HHmmss (N 6)

  /** Date, Expiration */
  14?: string; //  YYMM (N 4)

  /** Date, Reconciliation */
  15?: string; //  YYMMDD (N 6)

  /** Date, Conversion */
  16?: string; //  YYMMDD (N 6)

  /** Merchant Category Code - MCC */
  18?: string; //  N 4

  /** Country Code, Acquiring Institution */
  19?: string; //  N 3

  /** Point of Service Data Code */
  22?: any; //  AN 12 or object with subfields

  /** Card Sequence Number */
  23?: string; //  N 3

  /** Function Code */
  24?: string; //  N 3

  /** Message Reason Code */
  25?: string; //  AN 4

  /** Card Acceptor Business Code */
  26?: string; //  N 4

  /** Amounts, Original */
  30?: string; //  N 24

  /** Acquirer Institution ID Code */
  32: string; //  n..11

  /** Forwarding Institution ID Code */
  33?: string; //  n..11

  /** Acceptance Environment Data (TLV Format) */
  34?: string; //  z..37

  /** Track 2 Data */
  35?: string; //  z..37

  /** Track 3 Data */
  36?: string; //  z..104

  /** Retrieval Reference Number */
  37?: string; //  AN 12

  /** Approval Code */
  38?: string; //  AN 6

  /** Action Code */
  39?: string; //  AN 2

  /** Card Acceptor Terminal Identification */
  41?: string; //  AN 8

  /** Card Acceptor Identification Code */
  42?: string; //  AN 15

  /** Card Acceptor Name/Location */
  43?: any; //  AN..40 or object with subfields

  /** Additional Response Data */
  44?: string; //  AN..25

  /** Track 1 Data */
  45?: string; //  ANS..76

  /** Amounts, Fees */
  46?: string; //  AN..20

  /** Currency Code, Transaction */
  49?: string; //  A 3

  /** Currency Code, Reconciliation */
  50?: string; //  A 3

  /** Currency Code, Cardholder Billing */
  51?: string; //  A 3

  /** Personal Identification Number (PIN) Data */
  52?: string; //  B 8

  /** Amounts, Additional */
  54?: string; //  AN..120

  /** Integrated Circuit Card System Related Data */
  55?: string; //  ANS..999

  /** Original Data Elements */
  56?: string; //  ANS 28

  /** Authorization Life Cycle Code */
  57?: string; //  AN 2

  /** Authorizing Agent Institution ID Code */
  58?: string; //  AN 11

  /** Primary Reserved Private */
  62?: any; //  ANS..999 or object with subfields

  /** Data Record */
  72?: string; //  ANS..999

  /** Transaction Destination Institution Id Code */
  93?: string; //  AN 11

  /** Transaction Destination Originator Id Code */
  94?: string; //  AN 11

  /** Account Identification 1 */
  102?: string; //  an..34

  /** Reserved for Private Use */
  123?: any; //  ANS..999 or object with subfields

  /** Card ID Information */
  124?: any; //  It contains another response code "09" where we get the card ID
}

// ____________ ------------- CIP ------------- ____________

/**
 * Interface representing data for inserting a transaction directly into OraSys with given fee
 */
export interface InsertTxCIPInterfaceFields {
  transactionAmount: number;
  region: string;
  feeType: string;
  feeAmount: number;
  FXFeeAmount: number;
  feeNarrative: string;
  currency: string;
  retrievalReferenceNumber: string;
  IBAN: string;
  cardID: string;
  mcc: string;
}

/**
 * Interface representing data for inserting a transaction directly into OraSys, finding the right fee and applying it
 */
export interface InsertTxWithApplyingFeeCIPInterfaceFields {
  OperationType: OperationType;
  CustomerType: CustomerType;
  ProductType: ProductType;
  Location: CardFeeLocation;
  Modifier: string;
  transactionAmount: number;
  region: string;
  retrievalReferenceNumber: string;
  IBAN: string;
  cardID: string;
  mcc: string;
}

/**
 * Interface representing a cut down version of TransactionInterfaceData for CIP get-transactions end-point
 */
export interface SimpleTransaction {
  transactionProcessingCode: string;
  transactionAmount: number;
  billingAmount: number;
  transactionDatetime: string;
  conversionRate: string;
  traceNumber: string;
  cardExpiryDate: string;
  retrievalReference: string;
  terminalId: string;
  merchantId: string;
  merchantLocation: string;
  currencyTransaction: string;
  currencyBilling: string;
  accountIdentification: string;
  customFields: {
    transactionInfo: string;
    paypalReference: string;
  };
  resolvent: {
    type: TransactionResolventTypeEnum;
    resolved: boolean;
    date: string;
  };
  processingOutcome: {
    approved: boolean;
  };
  extendedData: {
    authorizationLevel: string;
    authorizationMethod: string;
    productCode: string;
    deviceId: string;
    channelId: string;
    riskScore: string;
    retryCount: string;
    trackingId: string;
  };
  fees: {
    taxType: string;
    taxAmount: number;
    taxAmountFx: number;
    taxNarrative: string;
  };
  transStatus: TransactionStatusEnum;
  transType: TransactionTypeData;
  externalToken: string;
  cardId: string;
}

/**
 * Interface representing query params for CIP get-transactions end-point
 */
export interface TransactionQueryParams {
  accountIdentification?: string;
  cardId?: string;
  status?: TransactionStatusEnum;
  type?: string;
  dateStart?: string;
  dateEnd?: string;
  page?: string; // string, since from req.query
  limit?: string;
}

/**
 * Interface representing response for CIP get-transactions end-point
 */
export interface TransactionResponse {
  transactions: SimpleTransaction[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

/**
 * Interface representing unique combination of fields to identify a transaction
 */
export interface TransactionUniqueCombinationFields {
  retrievalReferenceNumber: string;
  merchantCategoryCode: string;
  cardAcceptorTerminalId: string;
  cardAcceptorIdCode: string;
}
