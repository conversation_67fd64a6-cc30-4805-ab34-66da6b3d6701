/**
 * Enum for customer ID document types
 */
export enum CustomerIdType {
  ID = 'ID',
  PASSPORT = 'PASSPORT',
  RESIDENCE_CARD = 'RESIDENCE_CARD',
  DRIVING_LICENSE = 'DRIVING_LICENSE',
  MINOR_WITHOUT_ID = 'MINOR_WITHOUT_ID',
  OTHER = 'OTHER'
}

/**
 * Enum for client risk status levels
 */
export enum RiskStatus {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  VERY_HIGH = 'VERY HIGH'
}

/**
 * Enum for application status
 */
export enum ApplicationStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  SUBMITTED = 'SUBMITTED'
}

/**
 * Interface representing a client in the database
 */
export interface ClientInterface extends Document {
  origin: string;
  company: string;
  productVersion: string[];
  personalInfo: {
    firstName: string;
    middleName?: string;
    lastName: string;
    mothersMaidenName?: string;
    email: string;
    phone: string;
    authPhoneNumber: string;
    dateOfBirth: string;
    birthCountry: string;
  };
  address: {
    street: string;
    buildingNumber?: string;
    apartmentNumber?: string;
    city: string;
    stateProvince?: string;
    zipCode: string;
    country: string;
  };
  operationStatus: string;
  idDocument: {
    customerIdType: CustomerIdType;
    number: string;
    issueDate: string;
    expiryDate: string;
    issuingCountry: string;
    idAuthority: string;
  };
  taxInfo?: {
    country?: string;
    taxIdNumber?: string;
  };
  clientID: string;
  cardTypes: string[];
  cardCurrency: string[];
  client: boolean;
  permissions?: string[];
  legalId?: string;
  citizenship: string;
  applicationId: string;
  riskLevel: number;
  riskStatus: RiskStatus;
  applicationStatus: ApplicationStatus;
  applicationDate: string;
  dashboardStatus: string;
}
