/**
 * Interface representing a client in the database
 */
export interface ClientInterface extends Document {
  origin: string;
  company: string;
  productVersion: string[];
  personalInfo: {
    firstName: string;
    secondName?: string;
    lastName: string;
    mothersMaidenName?: string;
    email: string;
    phoneNumber: string;
    authPhoneNumber: string;
    birthDate: string;
    birthCountry: string;
  };
  address: {
    street: string;
    buildingNumber?: string;
    apartmentNumber?: string;
    city: string;
    stateProvince?: string;
    zipCode: string;
    country: string;
  };
  operationStatus: string;
  idDocument: {
    customerIdType: string;
    number: string;
    issueDate: string;
    expiryDate: string;
    issuingCountry: string;
    idAuthority: string;
  };
  taxInfo?: {
    country?: string;
    taxIdNumber?: string;
  };
  clientID: string;
  cardTypes: string[];
  cardCurrency: string[];
  client: boolean;
  legalId?: string;
  citizenship: string;
  applicationId: string;
  riskLevel: number;
  riskStatus: string;
  applicationStatus: string;
  applicationDate: string;
  dashboardStatus: string;
  createdAt?: string;
  updatedAt?: string;
}
