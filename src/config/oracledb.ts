import oracledb from 'oracledb';
import winston from 'winston';
import logger from '@submodules/ryvyl-commons/services/loggerService';
import oracleDBLogger from '../utils/logger';

const dbConfig = {
  user: process.env.ORACLE_USER,
  password: process.env.ORACLE_PASSWORD,
  connectString: process.env.ORACLE_CONNECTING_STRING,
  poolMin: 10,
  poolMax: 50,
  poolIncrement: 5,
  poolTimeout: 60,
  queueTimeout: 60000
};

let oraclePool: oracledb.Pool | undefined = undefined;

// Function to connect to Oracle DB
export async function connectToOracle() {
  try {
    logger.info('Connecting to Oracle...');
    const clientOpts = { libDir: process.env.LD_LIBRARY_PATH };
    oracledb.initOracleClient(clientOpts);
    oraclePool = await oracledb.createPool(dbConfig);
    logger.info('Oracle connection pool created successfully');
  } catch (err) {
    logger.error(`Cannot connect to Oracle ${(err as Error).message}`);
    throw err;
  }
}

// Function to disconnect from Oracle DB
export async function disconnectFromOracle() {
  try {
    await oracledb.getPool().close(10);
    logger.info('Oracle connection pool closed successfully');
  } catch (err) {
    logger.error(`Cannot disconnect from Oracle ${(err as Error).message}`);
    throw err;
  }
}

async function retryOperation<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
  let lastError;
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      oracleDBLogger.warn(`Operation failed, retrying (${i + 1}/${maxRetries}): ${(error as Error).message}`);
      await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, i))); // Exponential backoff
    }
  }
  throw lastError;
}

export async function executeOracleQuery(
  query: string,
  parameters?: any,
  selectedLogger: winston.Logger = oracleDBLogger
): Promise<any[] | undefined> {
  return new Promise(async (resolve, reject) => {
    let connection: oracledb.Connection | undefined;
    try {
      connection = await oracledb.getConnection();

      const timeout: Promise<never> = new Promise((_, timeoutReject) => {
        setTimeout(() => {
          timeoutReject(new Error('Query execution timeout'));
        }, 30000);
      });

      const result = await Promise.race([connection!.execute(query, parameters || {}), timeout]);
      resolve(result.rows);
    } catch (error: any) {
      selectedLogger.error(`Error executing Oracle query - ${error.message}`);
      reject(error); // Re-throw to trigger retry
    } finally {
      if (connection) {
        try {
          await connection.close();
        } catch (err) {
          selectedLogger.error(`Error closing oracle connection: ${(err as Error).message}`);
        }
      }
    }
  });
}

export async function executeOracleProcedure(
  procedureName: string,
  parameters: any,
  selectedLogger: winston.Logger = oracleDBLogger
): Promise<any> {
  let connection: oracledb.Connection | undefined;
  try {
    connection = await oracledb.getConnection();

    const paramNames = Object.keys(parameters);
    const procedureCall = `BEGIN ${procedureName}(${paramNames.map((name) => `:${name}`).join(', ')}); END;`;

    const result = await connection.execute(procedureCall, parameters, { autoCommit: true });

    return result.outBinds;
  } catch (error: any) {
    throw error;
  } finally {
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        selectedLogger.error(`Error closing oracle connection: ${(err as Error).message}`);
      }
    }
  }
}

export async function executeOracleProcedureWithParameterOrder(
  procedureName: string,
  parameters: any,
  parameterOrder: string[],
  selectedLogger: winston.Logger = oracleDBLogger
): Promise<any> {
  let connection: oracledb.Connection | undefined;
  try {
    connection = await oracledb.getConnection();

    // Create the procedure call that matches the SQL file exactly
    const procedureCall = `BEGIN
      :v_res := ${procedureName}(${parameterOrder.map((param) => `:${param}`).join(', ')});
    END;`;

    const result = await connection.execute(procedureCall, parameters, {
      autoCommit: true,
      outFormat: oracledb.OUT_FORMAT_OBJECT,
      bindDefs: {
        v_res: { type: oracledb.NUMBER, dir: oracledb.BIND_OUT },
        ip_acc_no: { type: oracledb.STRING, maxSize: 100 },
        ip_amount: { type: oracledb.NUMBER },
        ip_block_id: { type: oracledb.NUMBER },
        ip_card_no: { type: oracledb.STRING, maxSize: 100 },
        ip_terminal_id: { type: oracledb.STRING, maxSize: 100 },
        ip_authorization_code: { type: oracledb.STRING, maxSize: 100 },
        ip_stan: { type: oracledb.STRING, maxSize: 100 },
        ip_time: { type: oracledb.STRING, maxSize: 100 },
        ip_transaction_amount: { type: oracledb.NUMBER },
        ip_transaction_currency: { type: oracledb.STRING, maxSize: 100 },
        ip_place_transaction: { type: oracledb.STRING, maxSize: 100 },
        ip_type_transaction: { type: oracledb.STRING, maxSize: 100 },
        op_availability_before_request: { type: oracledb.NUMBER, dir: oracledb.BIND_OUT }
      }
    } as oracledb.ExecuteOptions);

    return result.outBinds;
  } catch (error: any) {
    throw error;
  } finally {
    if (connection) {
      try {
        await connection.close();
      } catch (err) {
        selectedLogger.error(`Error closing oracle connection: ${(err as Error).message}`);
      }
    }
  }
}

export async function executeOracleWriteQuery(
  query: string,
  parameters?: any
): Promise<{ success: boolean; message: string }> {
  try {
    await retryOperation(async () => {
      let connection: oracledb.Connection | undefined;
      try {
        connection = await oracledb.getConnection();

        const timeout: Promise<never> = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Write query execution timeout'));
          }, 30000);
        });

        const executeAndCommit = async () => {
          await connection!.execute(query, parameters || {});
          await connection!.commit();
        };

        await Promise.race([executeAndCommit(), timeout]);
      } catch (error: any) {
        oracleDBLogger.error('Error executing Oracle write query - ', error);
        throw error;
      } finally {
        if (connection) {
          try {
            await connection.close();
          } catch (err) {
            oracleDBLogger.error(`Error closing oracle connection: ${(err as Error).message}`);
          }
        }
      }
    });

    return { success: true, message: 'Query executed and committed successfully' };
  } catch (error: any) {
    return { success: false, message: `Execution failed: ${error.message || 'Unknown error'}` };
  }
}
