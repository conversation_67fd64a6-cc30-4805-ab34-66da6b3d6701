import { getEnvOrRevert, getListEnvironmentVariableAsArray } from '@submodules/ryvyl-commons/config';
import { normalizeStringNewlines } from '@submodules/ryvyl-commons/utils/helper';

export const oraAuthConfig = {
  ryvylOraAuthUrl: process.env.RYVYL_ORA_AUTH_URL
};

export const ITCardConfig = {
  ipWhitelist: getListEnvironmentVariableAsArray('IP_WHITELIST_ITCARD')
};

export const CIPConfig = {
  ryvylCIPUrl: getEnvOrRevert('RYVYL_CIP_URL'),
  ipWhitelist: getListEnvironmentVariableAsArray('IP_WHITELIST_CIP')
};

export const START_CRON_JOB = process.env.START_CRON_JOB === 'true' ? true : false;

export const SFTP_Configs = {
  settlement: {
    host: getEnvOrRevert('SFTP_HOST_IT_CARD'),
    port: Number(getEnvOrRevert('SFTP_PORT_IT_CARD')) || 22,
    username: getEnvOrRevert('SFTP_USER_IT_CARD'),
    privateKey: normalizeStringNewlines(getEnvOrRevert('SFTP_PRIVATE_KEY_IT_CARD'))
  },
  dropbox: {
    host: getEnvOrRevert('SFTP_HOST_DROPBOX'),
    port: Number(getEnvOrRevert('SFTP_PORT_DROPBOX')) || 22,
    username: getEnvOrRevert('SFTP_USER_DROPBOX'),
    privateKey: normalizeStringNewlines(getEnvOrRevert('SFTP_PRIVATE_KEY_DROPBOX'))
  }
};

export const resolvedTxCron = {
  daysToCheckForCron: START_CRON_JOB ? getEnvOrRevert('DAYS_TO_CHECK_BACK') : null,
  emailServiceUrl: START_CRON_JOB ? getEnvOrRevert('RYVYL_EMAIL_SERVICE_URL') : null
};

export const ONEDRIVE = {
  TENANT_ID: getEnvOrRevert('ONEDRIVE_TENANT_ID'),
  CLIENT_ID: getEnvOrRevert('ONEDRIVE_CLIENT_ID'),
  CLIENT_SECRET: getEnvOrRevert('ONEDRIVE_CLIENT_SECRET'),
  ONEDRIVE_EMAIL: getEnvOrRevert('ONEDRIVE_EMAIL'),
  SCOPE: 'https://graph.microsoft.com/.default'
};
