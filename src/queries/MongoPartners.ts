import logger from '../utils/logger';
import Partners from '../models/partnersSchema';

export async function createPartner(data: {
  url: string;
  name: string;
  publicKey: string;
  webhookApplicantUrl?: string;
}) {
  try {
    return await Partners.create(data);
  } catch (error) {
    logger.error('Error creating partner:', error);
    throw new Error('Failed to create partner');
  }
}

// ___________ ------------ Find ------------ ___________

export async function findPartnerByUrl(url: string) {
  try {
    return await Partners.findOne({ url });
  } catch (error) {
    logger.error('Error finding partner by url:', error);
    return null;
  }
}
