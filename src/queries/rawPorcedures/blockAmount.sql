declare
   ip_acc_no                      varchar2(100); /* Сметка Номер IBAN */
   ip_amount                      number; /* Сума за блокиране */
   ip_block_id                    number; /* Уникален номер за блокировка Идентификатор, който се използва и при деблокиране */
   ip_card_no                     varchar2(100); /* LENGTH : 20 */
   ip_terminal_id                 varchar2(100); /*  LENGTH : 8 */
   ip_authorization_code          varchar2(100); /* LENGTH :6   */
   ip_stan                        varchar2(100); /*  LENGTH : 6 */
   ip_time                        varchar2(100); /* LENGTH : 16 ФОРМАТ: (ДД.ММ.ГГГГ ЧЧ:МИ)  (10.08.2010 19:29)  */
   ip_transaction_amount          number; /* СУМА НА ТРАНЗАКЦИЯТА В ОРИГ. ВАЛУТА                         */
   ip_transaction_currency        varchar2(100); /* LENGTH : 3 – Currency char code by ISO : BGN, USD, EUR etc. */
   ip_place_transaction           varchar2(100); /* LENGTH : 40                                                 */
   ip_type_transaction            varchar2(100); /* LENGTH : 3  VALUES: ATM – банкомат (теглене) , PSG – ПОС на гише в банка (теглене), POS – ПОС при търговец + Internet (плащане)*/
   op_availability_before_request number; /* Разполагаема сума по сметката преди настоящата заявка  за блокиране. */
   v_res                          number; /* Върнатият резултат е равен на ip_amount при успешно блокиране   и   0 при неуспешно блокиране */
begin
   ip_acc_no := 'BG42TRUD40059780002505';
   ip_amount := to_number ( to_char(
      sysdate,
      '.SS'
   ) ) + 0.01;
   ip_block_id := to_number ( to_char(
      sysdate,
      'YYMMDDHH24MISS'
   ) );
  --
   v_res := orateu_importexport.aml_block_amount(
      ip_acc_no,
      ip_amount,
      ip_block_id,
      ip_card_no,
      ip_terminal_id,
      ip_authorization_code,
      ip_stan,
      ip_time,
      ip_transaction_amount,
      ip_transaction_currency,
      ip_place_transaction,
      ip_type_transaction,
      op_availability_before_request
   );
   commit;
  --
   dbms_output.put_line('AML_Block_Amount:');
   dbms_output.put_line('ip_acc_no: ' || ip_acc_no);
   dbms_output.put_line('v_res: ' || v_res);
   dbms_output.put_line('ip_block_id: ' || ip_block_id);
   dbms_output.put_line('op_availability_before_request: ' || op_availability_before_request);
end;