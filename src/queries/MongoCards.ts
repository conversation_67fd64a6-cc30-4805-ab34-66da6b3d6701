import Card from '../models/cardSchema';
import logger from '../utils/logger';

export const createCard = async (data: any) => {
  try {
    if (!data) {
      logger.error('No data provided for creating a debit account');
      return;
    }

    const newCard = new Card({
      cardHash: data.cardHash,
      cardKey: data.cardKey,
      expDate: data.expDate,
      status: data.status,
      statusCode: data.statusCode,
      kind: data.kind,
      productCode: data.productCode,
      productDesc: data.productDesc,
      main: data.main,
      holder: data.holder,
      accNo: data.accNo,
      embossName1: data.embossName1,
      cardMask: data.cardMask
    });

    const savedCard = await newCard.save();
    return savedCard;
  } catch (error) {
    logger.error('Error creating debit account:', error);
    throw error;
  }
};

// ___________ ------------ Find ------------ ___________

export const findCardByCardKey = async (cardKey: string) => {
  try {
    const card = await Card.findOne({ cardKey });
    return card;
  } catch (error) {
    logger.error('Error finding card by card key:', error);
    throw null;
  }
};
