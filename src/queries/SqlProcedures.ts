import oracledb from 'oracledb';
import { executeOracleProcedure, executeOracleProcedureWithParameterOrder } from '../config/oracledb';
import { oracleDBLogger } from '../utils/logger';

/**
 * Gets the balance and availability for a given IBAN
 * @param ip_acc_no the IBAN to get the balance for
 * @returns the balance and availability
 */
export async function getUserBalanceAndAvailabilityByIBAN(ip_acc_no: string) {
  try {
    const result = await executeOracleProcedure('ORATEU_importexport.AML_Report_Balance', {
      ip_acc_no: ip_acc_no,
      op_availability: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      op_account_balance: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER }
    });

    return result;
  } catch (error) {
    oracleDBLogger.error(`Error during procedure for getting account balance for IBAN ${ip_acc_no}: ${error}`);
    return null;
  }
}

/**
 * Gets the balance, availability, currency and more info about an account
 * @param ip_acc_no the IBAN to get the info for
 * @returns the balance, availability, currency and more info
 */
export async function getUserBalanceIIInfo(ip_acc_no: string) {
  try {
    const result = await executeOracleProcedure('ORATEU_importexport.AML_Report_BalanceII', {
      ip_acc_no: ip_acc_no,
      op_availability: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      op_account_balance: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      opAccBallance: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      opAvailableOverdraft: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      opBlockedAmount: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      opCurrency: { dir: oracledb.BIND_OUT, type: oracledb.STRING, maxSize: 3 }
    });

    return result;
  } catch (error) {
    oracleDBLogger.error(`Error during procedure for getting account balance info for IBAN ${ip_acc_no}: ${error}`);
    return null;
  }
}

/**
 * Blocks an amount for a given IBAN
 * @param params the parameters for the procedure
 * @returns the result of the procedure
 */
export async function blockAmountByIBAN(params: {
  ip_acc_no: string;
  ip_amount: number;
  ip_block_id: number; // Should be unique
  ip_card_no?: string;
  ip_terminal_id?: string;
  ip_authorization_code?: string;
  ip_stan?: string;
  ip_time?: string;
  ip_transaction_amount?: number;
  ip_transaction_currency?: string;
  ip_place_transaction?: string;
  ip_type_transaction?: string;
}): Promise<{
  success: boolean;
  data?: {
    v_res: number;
    op_availability_before_request: number;
  };
  error?: {
    code: string;
    message: string;
  };
}> {
  try {
    // This order must match exactly the procedure parameters
    const parameterOrder = [
      'ip_acc_no',
      'ip_amount',
      'ip_block_id',
      'ip_card_no',
      'ip_terminal_id',
      'ip_authorization_code',
      'ip_stan',
      'ip_time',
      'ip_transaction_amount',
      'ip_transaction_currency',
      'ip_place_transaction',
      'ip_type_transaction',
      'op_availability_before_request'
    ];

    const bindParams = {
      ip_acc_no: params.ip_acc_no,
      ip_amount: params.ip_amount,
      ip_block_id: params.ip_block_id,
      ip_card_no: params.ip_card_no || '',
      ip_terminal_id: params.ip_terminal_id || '',
      ip_authorization_code: params.ip_authorization_code || '',
      ip_stan: params.ip_stan || '',
      ip_time: params.ip_time || '',
      ip_transaction_amount: params.ip_transaction_amount || 0,
      ip_transaction_currency: params.ip_transaction_currency || '',
      ip_place_transaction: params.ip_place_transaction || '',
      ip_type_transaction: params.ip_type_transaction || '',
      op_availability_before_request: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER },
      v_res: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER }
    };

    const result = await executeOracleProcedureWithParameterOrder(
      'ORATEU_importexport.AML_Block_Amount',
      bindParams,
      parameterOrder
    );

    return {
      success: true,
      data: {
        v_res: result.v_res,
        op_availability_before_request: result.op_availability_before_request
      }
    };
  } catch (error) {
    oracleDBLogger.error(`Error during procedure AML_Block_Amount for IBAN ${params.ip_acc_no}:`, error);

    // Check for the specific Oracle constraint violation error
    if (
      error instanceof Error &&
      error.message.includes('ORA-00001') &&
      error.message.includes('AUTH_TRANSACTS_ID_BLOCK')
    ) {
      return {
        success: false,
        error: {
          code: 'DUPLICATE_BLOCK_ID',
          message: 'Block ID is already in use'
        }
      };
    }

    return {
      success: false,
      error: {
        code: 'PROCEDURE_ERROR',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    };
  }
}

/**
 * Unblocks an amount for a given block ID
 * @param ip_block_id the block ID to unblock
 * @returns the result of the procedure
 */
export async function unblockAmountByBlockID(ip_block_id: number) {
  try {
    // Using executeOracleProcedureWithParameterOrder for more control over parameter binding
    const parameterOrder = ['ip_block_id'];

    const bindParams = {
      ip_block_id: ip_block_id,
      v_res: { dir: oracledb.BIND_OUT, type: oracledb.NUMBER }
    };

    const result = await executeOracleProcedureWithParameterOrder(
      'ORATEU_importexport.AML_Unblock_Amount',
      bindParams,
      parameterOrder
    );

    return {
      v_res: result.v_res,
      ip_block_id: ip_block_id
    };
  } catch (error) {
    oracleDBLogger.error(`Error during procedure AML_Unblock_Amount for block ID ${ip_block_id}: ${error}`);
    return null;
  }
}
