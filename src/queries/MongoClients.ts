import Client from '../models/clientSchema'; // adjust the path to your actual model
import logger from '../utils/logger';

export const createClient = async (data: any) => {
  try {
    if (!data) {
      logger.error('No data provided for creating a client');
      return;
    }

    const newClient = new Client({
      origin: data.origin,
      company: data.company,
      productVersion: data.productVersion,
      personalInfo: data.personalInfo,
      address: data.address,
      operationStatus: data.operationStatus,
      idDocument: data.idDocument,
      taxInfo: data.taxInfo,
      clientID: data.clientID,
      cardTypes: data.cardTypes,
      cardCurrency: data.cardCurrency,
      client: data.client,
      permissions: data.permissions,
      legalId: data.legalId,
      citizenship: data.citizenship,
      applicationId: data.applicationId,
      riskLevel: data.riskLevel,
      riskStatus: data.riskStatus,
      applicationStatus: data.applicationStatus,
      applicationDate: data.applicationDate,
      dashboardStatus: data.dashboardStatus
    });

    const savedClient = await newClient.save();
    return savedClient;
  } catch (error) {
    logger.error('Error creating client:', error);
    throw error;
  }
};

// ___________ ------------ Find ------------ ___________

export const findClientByClientId = async (clientId: string) => {
  try {
    const client = await Client.findOne({ clientID: clientId });
    return client;
  } catch (error) {
    logger.error('Error finding client by client ID:', error);
    throw null;
  }
};
