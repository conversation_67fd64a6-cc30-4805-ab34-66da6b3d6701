import { TransactionSettlementOrasysRecord } from '../interfaces/settlementInterfaces';
import { executeOracleQuery, executeOracleWriteQuery } from '../config/oracledb';
import { oracleDBLogger } from '../utils/logger';

/**
 * Gets the last 10 customers from OraSys
 * @returns Array of customers
 */
export async function getAMLCustomersFromOrasys() {
  let selectedData;

  const selectDataSQL = `
    SELECT * FROM (
      SELECT * FROM AML_CHECK_CUSTS 
      ORDER BY clitype DESC
    )
    WHERE ROWNUM <= 10`;

  try {
    selectedData = (await executeOracleQuery(selectDataSQL)) ?? [];
  } catch (error) {
    oracleDBLogger.error('Error executing balance query:', error);
    selectedData = [];
  }

  return selectedData;
}

/**
 * Inserts data in OraSys for creating a transaction
 * @param transactionData the transaction data to be inserted
 * @returns success flag and message if failed
 */
export async function insertOraSysTransaction(transactionData: TransactionSettlementOrasysRecord) {
  // Get the latest UNIQUE_ID and increment it
  const latestUniqueIdQuery = 'SELECT MAX(UNIQUE_ID) FROM ORATEU_importexport.AML_IMP_IL_TRANSACS';
  const latestUniqueIdResult = await executeOracleQuery(latestUniqueIdQuery);
  const latestUniqueId = latestUniqueIdResult?.[0]?.[0] || 0;

  const binds = {
    uniqueId: latestUniqueId + 1,
    transDate: transactionData.transDate,
    schDate: transactionData.schDate,
    valueDate: transactionData.valueDate,
    transType: transactionData.transType,
    descriptor: transactionData.descriptor,
    mcc: transactionData.mcc,
    region: transactionData.region,
    authCode: transactionData.authCode,
    cardNo: transactionData.cardNo,
    accountNo: transactionData.accountNo,
    cardHolder: transactionData.cardHolder,
    transAmount: transactionData.transAmount,
    transCodval: transactionData.transCodval,
    cardAmount: transactionData.cardAmount,
    cardCodval: transactionData.cardCodval,
    setlAmount: transactionData.setlAmount,
    setlCodval: transactionData.setlCodval,
    taxAmount: transactionData.taxAmount,
    taxAmountFx: transactionData.taxAmountFx,
    idBlock: transactionData.idBlock,
    idRowSch: transactionData.idRowSch // не се попълва от IL
  };

  const insertTransactionSettlementQuery = `
  INSERT INTO ORATEU_importexport.AML_IMP_IL_TRANSACS (
    UNIQUE_ID,
    TRANS_DATE,
    SCH_DATE,
    VALUE_DATE,
    TRANS_TYPE,
    DESCRIPTOR,
    MCC,
    REGION,
    AUTH_CODE,
    CARD_NO,
    ACCOUNT_NO,
    CARD_HOLDER,
    TRANS_AMOUNT,
    TRANS_CODVAL,
    CARD_AMOUNT,
    CARD_CODVAL,
    SETL_AMOUNT,
    SETL_CODVAL,
    TAX_AMOUNT,
    TAX_AMOUNT_FX,
    ID_BLOCK,
    ID_ROW_SCH
  ) VALUES (
    :uniqueId,
    :transDate,
    :schDate,
    :valueDate,
    :transType,
    :descriptor,
    :mcc,
    :region,
    :authCode,
    :cardNo,
    :accountNo,
    :cardHolder,
    :transAmount,
    :transCodval,
    :cardAmount,
    :cardCodval,
    :setlAmount,
    :setlCodval,
    :taxAmount,
    :taxAmountFx,
    :idBlock,
    :idRowSch
  )
`;

  return await executeOracleWriteQuery(insertTransactionSettlementQuery, binds);
}
