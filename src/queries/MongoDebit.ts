import DebitAccount from '../models/debitSchema';
import logger from '../utils/logger';

export const createDebitAccount = async (data: any) => {
  try {
    if (!data) {
      logger.error('No data provided for creating a debit account');
      return;
    }

    const newAccount = new DebitAccount({
      owners: data.owners,
      customerType: data.customerType,
      onboarding: data.onboarding,
      accountNumber: data.accountNumber,
      accountHolder: data.accountHolder,
      currencyCode: data.currencyCode,
      currency: data.currency,
      relationship: data.relationship,
      bankName: data.bankName,
      status: data.status,
      clientCode: data.clientCode,
      extAppId: data.extAppId,
      productCode: data.productCode,
      productDesc: data.productDesc,
      bankNumber: data.bankNumber,
      openDate: data.openDate,
      balance: data.balance,
      _id: data._id,
      __v: data.__v
    });

    const savedAccount = await newAccount.save();
    return savedAccount;
  } catch (error) {
    logger.error('Error creating debit account:', error);
    throw error;
  }
};

// ___________ ------------ Find ------------ ___________

export const findDebitAccountByIBAN = async (IBAN: string) => {
  try {
    const debitAccount = await DebitAccount.findOne({ accountNumber: IBAN });
    return debitAccount;
  } catch (error) {
    logger.error('Error finding debit account by IBAN:', error);
    throw null;
  }
};

export const findDebitAccountClientId = async (clientCode: string) => {
  try {
    const debitAccount = await DebitAccount.findOne({ clientCode });
    return debitAccount;
  } catch (error) {
    logger.error('Error finding debit account by client code:', error);
    throw null;
  }
};
