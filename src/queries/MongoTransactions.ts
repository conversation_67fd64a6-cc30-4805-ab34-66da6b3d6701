import { Logger } from 'winston';

import defaultLogger from '../utils/logger';
import { Iso8583Transaction } from '../models/transactionISOSchema';
import {
  TransactionInterfaceNames,
  TransactionResolventTypeEnum,
  TransactionSourceEnum,
  TransactionStatusEnum,
  TransactionUniqueCombinationFields
} from '../interfaces/transactionInterface';
import { TransactionTypeData } from '../constants/transactionType';

// ___________ CREATE TRANSACTION ___________

export async function createTransaction(
  formattedData: TransactionInterfaceNames,
  source: TransactionSourceEnum,
  rawData: any,
  fees?: { taxType: string; taxAmount: number; taxAmountFx: number; taxNarrative: string },
  txTypeData?: TransactionTypeData,
  logger: Logger = defaultLogger
) {
  try {
    return await Iso8583Transaction.create({
      ...formattedData,
      source,
      initialBodyData: rawData,
      fees,
      transType: txTypeData
    });
  } catch (error) {
    logger.error('Error creating transaction:', error);
    throw new Error('Failed to create transaction');
  }
}

// ___________ UPDATE TRANSACTION ___________

export async function updateTransactionBlockId(txId: string, blockId: any, logger: Logger = defaultLogger) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, { blockId }).lean();
  } catch (error) {
    logger.error(`Error updating transaction blockId ${blockId} for ${txId}:`, error);
  }
}

export async function updateTransactionApprovalCode(
  txId: string,
  approvalCode: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, { approvalCode }).lean();
  } catch (error) {
    logger.error(`Error updating transaction approvalCode ${approvalCode} for ${txId}:`, error);
  }
}

export async function updateTransactionStatus(
  txId: string,
  status: TransactionStatusEnum,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, { transStatus: status }).lean();
  } catch (error) {
    logger.error(`Error updating transaction status ${status} for ${txId}:`, error);
  }
}

export async function updateTransactionProcessingOutcome(
  txId: string,
  approved: boolean,
  responseSend: any,
  logger: Logger = defaultLogger,
  txDataSendToOraSys?: any,
  blockDataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(approved === true && { 'processingOutcome.approved': approved }),
        'processingOutcome.responseSend': responseSend,
        ...(txDataSendToOraSys !== undefined && {
          'processingOutcome.txDataSendToOraSys': txDataSendToOraSys
        }),
        ...(blockDataSendToOraSys !== undefined && {
          'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys
        })
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction processing outcome approved: ${approved}, responseSend: ${responseSend}}, txDataSendToOraSys: ${txDataSendToOraSys}, blockDataSendToOraSys: ${blockDataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeResponse(
  txId: string,
  responseSend: any,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        'processingOutcome.responseSend': responseSend
      }
    }).lean();
  } catch (error) {
    logger.error(`Error updating transaction processing outcome response: ${responseSend}} for ${txId}:`, error);
  }
}

export async function addTransactionMessage(txId: string, message: string, logger: Logger = defaultLogger) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(`Error updating transaction result message "${message}" for ${txId}:`, error);
  }
}

export async function updateTransactionResolvent(
  txId: string,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        'resolvent.type': type,
        'resolvent.date': date,
        ...(resolved === true && { 'resolvent.resolved': true }) // By default false, once market as true, never back to false
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction resolvent type: ${type}, resolved: ${resolved}, date: ${date} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionReversal(
  txId: string,
  reversed: boolean,
  reversalData: any,
  responseSend: any,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(reversed === true && { 'reversal.reversed': reversed }),
        'reversal.reversalData': reversalData,
        'reversal.responseSend': responseSend
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction reversal reversed: ${reversed}, reversalData: ${reversalData}, responseSend: ${responseSend} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionSettlement(
  txId: string,
  settled: boolean,
  settlementData: any,
  dataSendToOraSys: any,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(settled === true && { 'settlement.settled': settled }),
        'settlement.settlementData': settlementData,
        'settlement.dataSendToOraSys': dataSendToOraSys
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction settlement settled: ${settled}, settlementData: ${settlementData}, responseSend: ${dataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

// ___________ GET TRANSACTIONS ___________

export async function getTransactionById(txId: string) {
  try {
    return await Iso8583Transaction.findById(txId).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by id:', error);
    return null;
  }
}

export async function getTransactionByApprovalCode(approvalCode: string) {
  try {
    return await Iso8583Transaction.findOne({ approvalCode }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by approval code:', error);
    return null;
  }
}

export async function getTransactionByBlockId(blockId: number) {
  try {
    return await Iso8583Transaction.findOne({ blockId }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by block id:', error);
    return null;
  }
}

export async function getTransactionByTraceNumber(traceNumber: number) {
  try {
    return await Iso8583Transaction.findOne({ stan: traceNumber }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by trace number:', error);
    return null;
  }
}

export async function getTransactionByRetrievalReferenceNumber(refNumber: string) {
  try {
    return await Iso8583Transaction.findOne({ retrievalReferenceNumber: refNumber }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by retrieval reference number:', error);
    return null;
  }
}

export async function getTransactionsByRetrievalReferenceNumber(refNumber: string) {
  try {
    return await Iso8583Transaction.find({ retrievalReferenceNumber: refNumber }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by retrieval reference number:', error);
    return null;
  }
}

export async function getTransactionsByUniqueFieldsCombination(
  transactionUniqueCombinationFields: TransactionUniqueCombinationFields
) {
  try {
    // 🧹 Clean the input object (remove extra spaces)
    const cleaned = Object.fromEntries(
      Object.entries(transactionUniqueCombinationFields).map(([key, value]) => [
        key,
        typeof value === 'string' ? value.trim() : value
      ])
    ) as TransactionUniqueCombinationFields;

    // 🔍 Query Mongo but also allow DB values to have trailing spaces using RegExp
    return await Iso8583Transaction.find({
      retrievalReferenceNumber: new RegExp(`^${cleaned.retrievalReferenceNumber}\\s*$`),
      cardAcceptorBusinessCode: new RegExp(`^${cleaned.cardAcceptorBusinessCode}\\s*$`),
      cardAcceptorTerminalId: new RegExp(`^${cleaned.cardAcceptorTerminalId}\\s*$`),
      cardAcceptorIdCode: new RegExp(`^${cleaned.cardAcceptorIdCode}\\s*$`)
    }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transaction by unique combination fields:', error);
    return null;
  }
}

export async function getAllTransactionsByIBAN(IBAN: string) {
  try {
    return await Iso8583Transaction.find({ accountIdentification1: IBAN }).lean();
  } catch (error) {
    defaultLogger.error('Error getting transactions by IBAN:', error);
    return [];
  }
}

export async function getUnresolvedTransactionsOlderThan(days: number) {
  try {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return await Iso8583Transaction.find({
      createdAt: { $lt: date },
      'resolvent.resolved': false
    }).lean();
  } catch (error) {
    defaultLogger.error(`Error getting not resolved transactions for ${days} days on: ${new Date()}`, error);
    return [];
  }
}

export async function getBiggestBlockId() {
  try {
    const tx = await Iso8583Transaction.findOne().sort({ blockId: -1 }).lean();
    return tx ? tx.blockId : 0;
  } catch (error) {
    defaultLogger.error('Error getting biggest block id:', error);
    return 0;
  }
}

// ___________ HANDLE MIXED TRANSACTION UPDATES ___________

export async function updateTransactionProcessingOutcomeBlockDataResolventAndAddMessage(
  txId: string,
  blockDataSendToOraSys: any,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  message: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys,
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        'resolvent.type': type,
        'resolvent.date': date
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction block data: ${blockDataSendToOraSys}, resolved: ${resolved}, type: ${type}, date: ${date}, message: ${message} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeApprovalCodeBlockIdStatusAndAddMessage(
  txId: string,
  blockId: number,
  approved: boolean,
  approvalCode: string,
  transStatus: TransactionStatusEnum,
  message: string,
  logger: Logger = defaultLogger,
  blockDataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        transStatus,
        blockId,
        approvalCode,
        ...(approved === true && { 'processingOutcome.approved': approved }),
        ...(blockDataSendToOraSys !== undefined && {
          'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction block id ${blockId}, approved: ${approved}, approvalCode: ${approvalCode}, transStatus: ${transStatus}, blockDataSendToOraSys: ${blockDataSendToOraSys}, message: ${message} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeWithMessage(
  txId: string,
  approved: boolean,
  responseSend: any,
  logger: Logger = defaultLogger,
  message: string,
  txDataSendToOraSys?: any,
  blockDataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(approved === true && { 'processingOutcome.approved': approved }),
        'processingOutcome.responseSend': responseSend,
        ...(txDataSendToOraSys !== undefined && {
          'processingOutcome.txDataSendToOraSys': txDataSendToOraSys
        }),
        ...(blockDataSendToOraSys !== undefined && {
          'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction processing outcome approved: ${approved}, responseSend: ${responseSend}, message: ${message}, txDataSendToOraSys: ${txDataSendToOraSys}, blockDataSendToOraSys: ${blockDataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeWithMessageAndStatus(
  txId: string,
  approved: boolean,
  responseSend: any,
  message: string,
  transStatus: TransactionStatusEnum,
  logger: Logger = defaultLogger,
  txDataSendToOraSys?: any,
  blockDataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(approved === true && { 'processingOutcome.approved': approved }),
        'processingOutcome.responseSend': responseSend,
        transStatus,
        ...(txDataSendToOraSys !== undefined && {
          'processingOutcome.txDataSendToOraSys': txDataSendToOraSys
        }),
        ...(blockDataSendToOraSys !== undefined && {
          'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction processing outcome approved: ${approved}, responseSend: ${responseSend}, message: ${message}, transStatus: ${transStatus}, txDataSendToOraSys: ${txDataSendToOraSys}, blockDataSendToOraSys: ${blockDataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
  txId: string,
  approved: boolean,
  resolved: boolean,
  date: string,
  type: TransactionResolventTypeEnum,
  responseSend: any,
  message: string,
  transStatus: TransactionStatusEnum,
  logger: Logger = defaultLogger,
  txDataSendToOraSys?: any,
  blockDataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        'resolvent.date': date,
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        'resolvent.type': type,
        ...(approved === true && { 'processingOutcome.approved': approved }),
        'processingOutcome.responseSend': responseSend,
        transStatus,
        ...(txDataSendToOraSys !== undefined && {
          'processingOutcome.txDataSendToOraSys': txDataSendToOraSys
        }),
        ...(blockDataSendToOraSys !== undefined && {
          'processingOutcome.blockDataSendToOraSys': blockDataSendToOraSys
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction processing outcome approved: ${approved}, resolved: ${resolved}, date: ${date}, type: ${type}, responseSend: ${responseSend}, message: ${message}, transStatus: ${transStatus}, txDataSendToOraSys: ${txDataSendToOraSys}, blockDataSendToOraSys: ${blockDataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionSettlementWithMessage(
  txId: string,
  settled: boolean,
  settlementData: any,
  dataSendToOraSys: any,
  message: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(settled === true && { 'settlement.settled': settled }),
        'settlement.settlementData': settlementData,
        'settlement.dataSendToOraSys': dataSendToOraSys
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction settlement settled: ${settled}, settlementData: ${settlementData}, dataSendToOraSys: ${dataSendToOraSys}, message: ${message} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionReversalWithMessage(
  txId: string,
  reversed: boolean,
  reversalData: any,
  responseSend: any,
  message: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(reversed === true && { 'reversal.reversed': reversed }),
        'reversal.reversalData': reversalData,
        'reversal.responseSend': responseSend
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction reversal reversed: ${reversed}, reversalData: ${reversalData}, responseSend: ${responseSend}, message: ${message} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionReversalWithStatusAndResolventWithMessage(
  txId: string,
  reversed: boolean,
  transStatus: TransactionStatusEnum,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  message: string,
  logger: Logger = defaultLogger,
  reversalData?: any,
  responseSend?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        transStatus,
        'resolvent.type': type,
        'resolvent.date': date,
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        ...(reversed === true && { 'reversal.reversed': reversed }),
        ...(reversalData !== undefined && {
          'reversal.reversalData': reversalData
        }),
        ...(responseSend !== undefined && {
          'reversal.responseSend': responseSend
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction reversal reversed: ${reversed}, transStatus: ${transStatus}, resolved: ${resolved}, type: ${type}, date: ${date}, message: ${message}, reversalData: ${reversalData}, responseSend: ${responseSend} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionSettlementWithStatusAndResolventWithMessage(
  txId: string,
  settled: boolean,
  transStatus: TransactionStatusEnum,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  message: string,
  logger: Logger = defaultLogger,
  settlementData?: any,
  dataSendToOraSys?: any
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        transStatus,
        'resolvent.type': type,
        'resolvent.date': date,
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        ...(settled === true && { 'settlement.settled': settled }),
        ...(settlementData !== undefined && {
          'settlement.settlementData': settlementData
        }),
        ...(dataSendToOraSys !== undefined && {
          'settlement.dataSendToOraSys': dataSendToOraSys
        })
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction settled: ${settled}, transStatus: ${transStatus}, resolved: ${resolved}, type: ${type}, date: ${date}, message: ${message}, settlementData: ${settlementData}, dataSendToOraSys: ${dataSendToOraSys} for ${txId}:`,
      error
    );
  }
}

export async function updateTransactionProcessingOutcomeResponseWithMessageAndResolvent(
  txId: string,
  responseSend: any,
  message: string,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        'processingOutcome.responseSend': responseSend,
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        'resolvent.type': type,
        'resolvent.date': date
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction processing outcome response: ${responseSend}, message: ${message}, resolved: ${resolved}, type: ${type}, date: ${date} for ${txId}:`,
      error
    );
  }
}

export async function addTransactionMessageAndResolvent(
  txId: string,
  message: string,
  resolved: boolean,
  type: TransactionResolventTypeEnum,
  date: string,
  logger: Logger = defaultLogger
) {
  try {
    await Iso8583Transaction.findByIdAndUpdate(txId, {
      $set: {
        ...(resolved === true && { 'resolvent.resolved': resolved }),
        'resolvent.type': type,
        'resolvent.date': date
      },
      $push: {
        messages: message
      }
    }).lean();
  } catch (error) {
    logger.error(
      `Error updating transaction result message "${message}", resolved: ${resolved}, type: ${type}, date: ${date} for ${txId}:`,
      error
    );
  }
}
