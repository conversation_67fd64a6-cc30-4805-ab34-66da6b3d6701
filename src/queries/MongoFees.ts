import { card<PERSON>ee<PERSON>ogger } from '../utils/logger';
import { CardFeeModel, FXRateModel } from '../models/cardFee';
import { CardFee, FXRate } from '../interfaces/feeTypes';
import { CardFeeLocation, CustomerType, FXRatesCurrency, OperationType, ProductType } from '../constants/fees';

/**
 * Creates a new card fee in the database
 * @param data - The card fee data to be created
 * @returns The newly created card fee document
 * @throws Error if creation fails
 */
export async function createCardFee(data: CardFee) {
  try {
    return await CardFeeModel.create(data);
  } catch (error) {
    cardFeeLogger.error('Error creating card fee:', error);
    throw new Error('Failed to create card fee');
  }
}

/**
 * Retrieves all card fees from the database
 * @returns Array of all card fee documents
 */
export async function getAllCardFees() {
  try {
    return await CardFeeModel.find({});
  } catch (error) {
    cardFeeLogger.error('Error getting all card fees:', error);
    return [];
  }
}

/**
 * Finds card fees matching the provided filter criteria
 * @param filter - Object containing properties to filter by
 * @returns Array of matching card fee documents
 */
export async function findCardFees(filter: Partial<CardFee>) {
  try {
    return await CardFeeModel.find(filter);
  } catch (error) {
    cardFeeLogger.error('Error finding card fees with filter:', error);
    return [];
  }
}

/**
 * Finds a card fee by its unique fields
 * @param operationType - The operation type of the card fee
 * @param customerType - The customer type of the card fee
 * @param productType - The product type of the card fee
 * @param location - The location of the card fee
 * @param modifier - The modifier of the card fee
 * @returns The matching card fee document or null if not found
 */
export async function findCardFeeByUniqueFields(
  operationType: OperationType,
  customerType: CustomerType,
  productType: ProductType,
  location?: CardFeeLocation,
  modifier?: string
) {
  try {
    return await CardFeeModel.findOne({ operationType, customerType, productType, location, modifier });
  } catch (error) {
    cardFeeLogger.error('Error finding card fee with unique fields:', error);
    return null;
  }
}

// __________ FX Rate Fees __________

/**
 * Creates a new FX rate in the database
 * @param data - The FX rate data to be created
 * @returns The newly created FX rate document
 * @throws Error if creation fails
 */
export async function createFXRate(data: FXRate) {
  try {
    return await FXRateModel.create(data);
  } catch (error) {
    cardFeeLogger.error('Error creating FX rate:', error);
    throw new Error('Failed to create FX rate');
  }
}

/**
 * Finds an FX rate by its currency and client ID
 * @param currency - The currency of the FX rate
 * @param clientID - The client ID of the FX rate
 * @returns The matching FX rate document or null if not found
 */
export async function findFXRateByCurrencyAndClientID(currency: FXRatesCurrency, clientID: string) {
  try {
    return await FXRateModel.findOne({ currency, clientID });
  } catch (error) {
    cardFeeLogger.error('Error finding FX rate:', error);
    return null;
  }
}

/**
 * Finds an FX rate by its currency and client ID
 * @param currency - The currency of the FX rate
 * @param clientID - The client ID of the FX rate
 * @returns The matching FX rate document or null if not found
 */
export async function findFXRateByCurrency(currency: string) {
  try {
    return await FXRateModel.findOne({ currency });
  } catch (error) {
    cardFeeLogger.error('Error finding FX rate:', error);
    return null;
  }
}

/**
 * Retrieves all FX rates from the database
 * @returns Array of all FX rate documents
 */
export async function getAllFXRates() {
  try {
    return await FXRateModel.find({});
  } catch (error) {
    cardFeeLogger.error('Error getting all FX rates:', error);
    return [];
  }
}

/**
 * Finds FX rates matching the provided filter criteria
 * @param filter - Object containing properties to filter by
 * @returns Array of matching FX rate documents
 */
export async function findFXRates(filter: Partial<FXRate>) {
  try {
    return await FXRateModel.find(filter);
  } catch (error) {
    cardFeeLogger.error('Error finding FX rates with filter:', error);
    return [];
  }
}
