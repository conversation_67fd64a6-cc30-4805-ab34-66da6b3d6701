import Joi from 'joi';

export const registerCardSchema = Joi.object({
  event: Joi.string().valid('card_created').required(),

  cardData: Joi.object({
    cardHash: Joi.string().hex().length(128).required(),

    cardKey: Joi.string().required(),

    expDate: Joi.string()
      .pattern(/^(0[1-9]|1[0-2])\/\d{4}$/)
      .required(),

    status: Joi.string().valid('ORDERED', 'ACTIVE', 'BLOCKED', 'INACTIVE', 'CLOSED').required(),

    statusCode: Joi.string().alphanum().required(),

    kind: Joi.string().valid('DEBIT', 'CREDIT', 'PREPAID').required(),

    productCode: Joi.string().alphanum().required(),

    productDesc: Joi.string().required(),

    main: Joi.boolean().required(),

    holder: Joi.string()
      .pattern(/^RYVL-\d+$/)
      .required(),

    accNo: Joi.string()
      .pattern(/^BG\d{2}[A-Z]{4}\d{14}$/) // IBAN for Bulgaria
      .required(),

    embossName1: Joi.string().required(),

    cardMask: Joi.string()
      .pattern(/^\d{6}\*{6}\d{4}$/)
      .required()
  }).required()
});
