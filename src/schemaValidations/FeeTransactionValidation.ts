import Joi from 'joi';
import { CardFeeLocation, CustomerType, OperationType, ProductType } from '../constants/fees';
import {
  InsertTxWithApplyingFeeCIPInterfaceFields,
  InsertTxCIPInterfaceFields
} from '../interfaces/transactionInterface';

/**
 * Schema for inserting a transaction directly into OraSys with given fee
 */
export const transactionWithFeeCIPSchema = Joi.object<InsertTxCIPInterfaceFields>({
  transactionAmount: Joi.number().min(0).precision(2).required(),

  region: Joi.string().required().length(2),

  feeType: Joi.string().required(),

  feeAmount: Joi.number().min(0).precision(2).required(),

  FXFeeAmount: Joi.number().min(0).precision(2).required(),

  feeNarrative: Joi.string().required(),

  currency: Joi.string().length(3).uppercase().required(),

  retrievalReferenceNumber: Joi.string().pattern(/^\d+$/).required(),

  IBAN: Joi.string()
    .pattern(/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/)
    .required(),

  cardID: Joi.string().optional(),

  mcc: Joi.string()
    .pattern(/^\d{4}$/)
    .required()
});

/**
 * Schema for checking if a fee exists for a transaction and what is its amount
 */
export const checkFeeForTxSchema = Joi.object({
  OperationType: Joi.string()
    .required()
    .valid(...Object.values(OperationType)),

  CustomerType: Joi.string()
    .required()
    .valid(...Object.values(CustomerType)),

  ProductType: Joi.string()
    .required()
    .valid(...Object.values(ProductType)),

  Location: Joi.string()
    .length(2)
    .optional()
    .valid(...Object.values(CardFeeLocation)),

  Modifier: Joi.string().optional()
}).unknown();

/**
 * Schema for applying a fee on a transaction and inserting it directly into OraSys
 */
export const applyFeeOnTxSchema = Joi.object<InsertTxWithApplyingFeeCIPInterfaceFields>({
  OperationType: Joi.string()
    .required()
    .valid(...Object.values(OperationType)),

  CustomerType: Joi.string()
    .required()
    .valid(...Object.values(CustomerType)),

  ProductType: Joi.string()
    .required()
    .valid(...Object.values(ProductType)),

  Location: Joi.string()
    .length(2)
    .optional()
    .valid(...Object.values(CardFeeLocation)),

  Modifier: Joi.string().optional(),

  transactionAmount: Joi.number().min(0).precision(2).optional(),

  region: Joi.string().optional(),

  retrievalReferenceNumber: Joi.string().pattern(/^\d+$/).required(),

  IBAN: Joi.string()
    .pattern(/^[A-Z]{2}\d{2}[A-Z0-9]{11,30}$/)
    .required(),

  cardID: Joi.string().optional(),

  mcc: Joi.string()
    .pattern(/^\d{4}$/)
    .optional()
});
