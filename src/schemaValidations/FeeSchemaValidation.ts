import Joi from 'joi';
import { <PERSON><PERSON>ee, FXRate } from '../interfaces/feeTypes';
import {
  CardFeeCurrency,
  CardFeeLocation,
  CardFeeTrigger,
  CardFeeType,
  CustomerType,
  FXRatesCurrency,
  OperationType,
  ProductType
} from '../constants/fees';

export const cardFeeSchema = Joi.object<CardFee>({
  GLAccountName: Joi.string().optional(),

  feeCode: Joi.string().required(),

  description: Joi.string().required(),

  operationType: Joi.string()
    .required()
    .valid(...Object.values(OperationType)),

  customerType: Joi.string()
    .required()
    .valid(...Object.values(CustomerType)),

  productType: Joi.string()
    .required()
    .valid(...Object.values(ProductType)),

  location: Joi.string()
    .length(2)
    .optional()
    .valid(...Object.values(CardFeeLocation)),

  modifier: Joi.string().optional(),

  trigger: Joi.string()
    .required()
    .valid(...Object.values(CardFeeTrigger)),

  loopOnCard: Joi.boolean().optional(),

  displayDescriptor: Joi.string().required(),

  criteria: Joi.string().optional(),

  type: Joi.string()
    .required()
    .valid(...Object.values(CardFeeType)),

  currency: Joi.string()
    .length(3)
    .required()
    .valid(...Object.values(CardFeeCurrency)),

  fixedFee: Joi.alternatives()
    .try(
      Joi.string()
        .pattern(/^\d+([,.]\d{2})?$/, { name: 'decimal numbers' })
        .messages({
          'string.pattern.name': 'fixedFee must be a valid number with two decimal places.'
        }),
      Joi.number().precision(2)
    )
    .required(),

  percentageFee: Joi.number().precision(4).optional().default(0),

  supportsFXFee: Joi.boolean().optional()
});

export const fxRateSchema = Joi.object<FXRate>({
  flatFee: Joi.alternatives()
    .try(
      Joi.string()
        .pattern(/^\d+([,.]\d{2})?$/, { name: 'decimal numbers' })
        .messages({
          'string.pattern.name': 'flatFee must be a valid number with two decimal places.'
        }),
      Joi.number().precision(2)
    )
    .required(),

  percentage: Joi.number().precision(4).required(),

  currency: Joi.string()
    .length(3)
    .required()
    .valid(...Object.values(FXRatesCurrency)),

  clientID: Joi.string().required()
});
