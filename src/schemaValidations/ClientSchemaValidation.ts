import Joi from 'joi';
import { CustomerType } from '../constants/fees';

export const createClientAndDebitAccountSchema = Joi.object({
  client: Joi.object({
    clientCode: Joi.string()
      .pattern(/^RYVL-\d+$/)
      .required(),

    personalInfo: Joi.object({
      firstName: Joi.string().required(),
      secondName: Joi.string().allow('').optional(),
      lastName: Joi.string().required(),
      mothersMaidenName: Joi.string().required(),
      birthDate: Joi.date().iso().required(),
      email: Joi.string().email().required(),
      phoneNumber: Joi.string()
        .pattern(/^\+?\d+$/)
        .required(),
      authPhoneNumber: Joi.string()
        .pattern(/^\+?\d+$/)
        .required(),
      birthCountry: Joi.string().length(3).required()
    }).required(),

    address: Joi.object({
      street: Joi.string().required(),
      buildingNumber: Joi.string().required(),
      apartmentNumber: Joi.string().allow('').required(),
      city: Joi.string().required(),
      stateProvince: Joi.string().required(),
      zipCode: Joi.string().required(),
      country: Joi.string().length(3).required()
    }).required(),

    idDocument: Joi.object({
      customerIdType: Joi.string().valid('PASSPORT', 'ID_CARD', 'DRIVER_LICENSE').required(),
      number: Joi.string().required(),
      issueDate: Joi.date().iso().required(),
      expiryDate: Joi.date().iso().required(),
      issuingCountry: Joi.string().length(3).required(),
      idAuthority: Joi.string().required()
    }).required(),

    taxInfo: Joi.object({
      country: Joi.string().length(3).required(),
      taxIdNumber: Joi.string().required()
    }).required(),

    legalId: Joi.string().required(),
    citizenship: Joi.string().length(3).required(),
    applicationId: Joi.string().required(),
    riskLevel: Joi.string().required(),
    riskStatus: Joi.string().valid('LOW', 'MEDIUM', 'HIGH').required(),
    applicationStatus: Joi.string().valid('PENDING', 'APPROVED', 'REJECTED').required(),
    applicationDate: Joi.string()
      .pattern(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)
      .required(),

    company: Joi.string().required(),

    productVersions: Joi.array().items(Joi.string()).required()
  }).required(),

  debit: Joi.object({
    accountNumber: Joi.string().required(),
    customerType: Joi.string()
      .valid(...Object.values(CustomerType))
      .required(),
    currencyCode: Joi.string().required(),
    status: Joi.string().required(),
    extAppId: Joi.string().required(),
    productCode: Joi.string().required(),
    productDesc: Joi.string().required(),
    bankNumber: Joi.string().required(),
    openDate: Joi.date().iso().required(),
    bankName: Joi.string().required(),
    currencyName: Joi.string().required(),
    relationship: Joi.string().required()
  }).required()
});
