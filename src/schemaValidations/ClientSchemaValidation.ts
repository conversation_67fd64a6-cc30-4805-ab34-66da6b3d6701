import Joi from 'joi';
import { CustomerType } from '../constants/fees';
import { CustomerIdType, RiskStatus, ApplicationStatus } from '../interfaces/clientInterface';
import { AccountStatus } from '../interfaces/debitInterface';

export const createClientAndDebitAccountSchema = Joi.object({
  client: Joi.object({
    clientCode: Joi.string()
      .pattern(/^RYVL-[A-Z0-9]+$/)
      .max(15)
      .required(),

    personalInfo: Joi.object({
      firstName: Joi.string().max(40).required(),
      secondName: Joi.string().max(40).allow('').optional(),
      lastName: Joi.string().max(40).required(),
      mothersMaidenName: Joi.string().max(40).optional(),
      birthDate: Joi.date().iso().required(),
      email: Joi.string().email().max(40).required(),
      phoneNumber: Joi.string()
        .pattern(/^\+?\d+$/)
        .max(16)
        .required(),
      authPhoneNumber: Joi.string()
        .pattern(/^\+?\d+$/)
        .max(16)
        .required(),
      birthCountry: Joi.string().max(40).required()
    }).required(),

    address: Joi.object({
      street: Joi.string().max(40).required(),
      buildingNumber: Joi.string().max(40).optional(),
      apartmentNumber: Joi.string().max(10).allow('').optional(),
      city: Joi.string().max(40).required(),
      stateProvince: Joi.string().max(40).optional(),
      zipCode: Joi.string().max(10).required(),
      country: Joi.string().min(2).max(3).required() // Allow 2 or 3 letter country codes
    }).required(),

    idDocument: Joi.object({
      customerIdType: Joi.string()
        .valid(...Object.values(CustomerIdType))
        .max(20)
        .required(),
      number: Joi.string().max(20).required(),
      issueDate: Joi.date().iso().required(),
      expiryDate: Joi.date().iso().required(),
      issuingCountry: Joi.string().max(40).required(),
      idAuthority: Joi.string().max(40).required()
    }).required(),

    taxInfo: Joi.object({
      country: Joi.string().min(2).max(3).optional(), // Allow 2 or 3 letter country codes
      taxIdNumber: Joi.string().max(40).optional()
    }).optional(),

    legalId: Joi.string().max(11).optional(),
    citizenship: Joi.string().max(40).required(),
    applicationId: Joi.string().max(40).required(),
    riskLevel: Joi.number().integer().min(0).max(999).required(),
    riskStatus: Joi.string()
      .valid(...Object.values(RiskStatus))
      .max(9)
      .required(),
    applicationStatus: Joi.string()
      .valid(...Object.values(ApplicationStatus))
      .max(10)
      .required(),
    applicationDate: Joi.string()
      .pattern(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)
      .max(22)
      .required(),

    company: Joi.string().required(),

    productVersions: Joi.array().items(Joi.string()).required()
  }).required(),

  debit: Joi.object({
    accountNumber: Joi.string().max(26).required(),
    customerType: Joi.string()
      .valid(...Object.values(CustomerType))
      .required(),
    currencyCode: Joi.string().max(3).required(),
    status: Joi.string()
      .custom((value, helpers) => {
        const upper = value.toUpperCase();
        if (!Object.values(AccountStatus).includes(upper as AccountStatus)) {
          return helpers.error('any.invalid');
        }
        return upper;
      }, 'Normalize status to uppercase')
      .max(6)
      .required(),
    extAppId: Joi.string().max(40).optional(),
    productCode: Joi.string().max(4).required(),
    productDesc: Joi.string().max(225).required(),
    bankNumber: Joi.string().max(20).required(),
    bankName: Joi.string().max(30).required(),
    openDate: Joi.date().iso().optional(),
    currencyName: Joi.string().required(),
    relationship: Joi.string().required()
  }).required()
});
