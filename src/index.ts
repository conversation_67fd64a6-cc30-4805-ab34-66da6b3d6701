import { Express } from 'express';
import dotenv from 'dotenv';
import http from 'http';
import 'module-alias/register';

dotenv.config();

import logger from '@submodules/ryvyl-commons/services/loggerService';
import mongoose from '@submodules/ryvyl-commons/utils/mongoose';
import { cronDailyResolve, cronDailySettlement } from './services/cronService';
import { connectToOracle } from './config/oracledb';
import { START_CRON_JOB } from './config';
import { app, partnersApp } from './app';
import { getLatestBlockId } from './services/transactionService';

let server: http.Server | undefined;
let partnersServer: http.Server | undefined;

const PORT = process.env.PORT || 3000;
const PARTNERS_PORT = process.env.PARTNERS_PORT || 32551;

mongoose.connectDB(() => afterConnect(app), false);

app.on('ready', async () => {
  server = app.listen(PORT, () => logger.info(`Server is listening on port: ${PORT}`));
  partnersServer = partnersApp.listen(PARTNERS_PORT, () =>
    logger.info(`Partners server is listening on port: ${PARTNERS_PORT}`)
  );

  await connectToOracle();

  if (START_CRON_JOB) {
    initializeCronProcesses();
  }
});

process.on('SIGTERM', () => {
  closeServers('SIGTERM');
});

// To disconnect the server when we press 'ctrl + c' on the terminal
process.on('SIGINT', () => {
  closeServers('SIGINT');
});

/**
 * Function to be executed after successful connection
 * @param app The Express app
 */
async function afterConnect(app: Express): Promise<void> {
  getLatestBlockId();
  app.emit('ready');
}

async function initializeCronProcesses() {
  cronDailySettlement();
  cronDailyResolve();
}

function closeServer(server: http.Server) {
  server.close(() => {
    logger.info('The server has been stopped.');
  });
}

function closeServers(signal: string) {
  logger.info(`Received signal "${signal}", shutting down...`);

  if (partnersServer) closeServer(partnersServer);
  if (server) closeServer(server);

  logger.info('Exiting process...');
  process.exit(0);
}
