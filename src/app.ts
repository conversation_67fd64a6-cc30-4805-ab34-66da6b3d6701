import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import routes from './routes/routes';
import partnersRouter from './routes/partnersRoutes';
import parseBody from './utils/bodyParser';

const app = express();
const partnersApp = express();

app.use(helmet());
app.use(cors());
app.use(express.json());
app.use('', routes);

partnersApp.use(cors());
partnersApp.use(parseBody);
partnersApp.use(partnersRouter);

export { app, partnersApp };
