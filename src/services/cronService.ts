import { XMLParser } from 'fast-xml-parser';
import fs from 'fs/promises';
import path from 'path';
import { resolveCronLogger, settlementCronLogger } from '../utils/logger';
import { StartCronJob } from '../../submodules/ryvyl-commons/services/cronService';
import {
  addTransactionMessage,
  getUnresolvedTransactionsOlderThan,
  updateTransactionReversalWithStatusAndResolventWithMessage
} from '../queries/MongoTransactions';
import { unblockAmountByBlockID } from '../queries/SqlProcedures';
import { resolvedTxCron } from '../config';
import { makeBackendRequest, normalizeToArray } from '../utils/helpers';
import { TransactionResolventTypeEnum, TransactionStatusEnum } from '../interfaces/transactionInterface';
import { handleSFTP } from '../middlewares/SFTP';
import { handleSettlementFile, isRejectedFile } from './settlementService';
import { uploadFilesToOneDrive } from './onceDriveService';
import { OneDriveUploadOptions } from '../interfaces/oneDriveInterface';
import { returnDirectionTypeAsString } from './transactionService';

// ______------ Settlement Cronjob ------______
export async function cronDailySettlement() {
  await StartCronJob(
    {
      name: 'Daily Settlement',
      logger: settlementCronLogger,
      jobFunction: SettleTransactions
    },
    '30 2 * * *', // 2:30 am
    {
      scheduled: true,
      timezone: 'UTC' // Change to your desired timezone
    }
  );
}

// ______------ Resolve Cronjob ------______
export async function cronDailyResolve() {
  await StartCronJob(
    {
      name: 'Daily Resolve',
      logger: resolveCronLogger,
      jobFunction: UnblockNotResolvedTx
    },
    '15 2 * * *', // 2:15 am
    {
      scheduled: true,
      timezone: 'UTC' // Change to your desired timezone
    }
  );
}

// ___________------------- Settle Transactions -------------___________
/**
 * Downloads all XML files from SFTP, converts them to JSON, and sends them to OraSys
 * Afterwards, uploads the files to Dropbox, deletes the files from SFTP and the downloads folder
 */
async function SettleTransactions() {
  settlementCronLogger.info('Daily settlement cron job started');

  // 1 - Download all XML files from SFTP IT Card
  try {
    await handleSFTP('settlement', {
      action: 'download-all-xml',
      remoteDir: '/RYVYL_SETTLEMENTS',
      localDir: 'downloads'
    });
  } catch (error) {
    settlementCronLogger.error('Error downloading xml files from SFTP IT Card', error);
    return;
  }

  // 1.1 - Check if there are any XML files to settle
  let xmlFiles: string[] = [];
  try {
    const allFiles = await fs.readdir('downloads');
    xmlFiles = allFiles.filter((file) => path.extname(file).toLowerCase() === '.xml');
  } catch (err) {
    settlementCronLogger.error('Failed to read downloads directory', err);
    return;
  }

  if (xmlFiles.length === 0) {
    settlementCronLogger.info('No XML files to settle');
    return;
  }

  // 1.2 - Convert all XML files to JSON
  let parsedJSONs: { json: any; originalFileName: string }[] = [];
  try {
    for (const file of xmlFiles) {
      const xmlContent = await fs.readFile(`downloads/${file}`, 'utf-8');
      const parser = new XMLParser({
        ignoreAttributes: false,
        processEntities: false // secure config
      });

      const parsedJson = parser.parse(xmlContent); // converts XML → JSON
      parsedJSONs.push({ json: parsedJson, originalFileName: file });
    }
  } catch (error) {
    settlementCronLogger.error('Error converting XML to JSON', error);
    return;
  }

  // 2 - Handle all transactions from the JSON and send to OraSys
  const listOfFilesWithError = [];
  const failedFilesWithErrors = [];
  for (const { json, originalFileName } of parsedJSONs) {
    try {
      const isRejected = isRejectedFile(originalFileName);
      // 2.1 - For now it is expected to not receive reject files, but if we do, send email and continue
      // @Todo: Handling reject files is still TBD, after needed discussions add the needed logic
      if (isRejected) {
        const transactionsSettlements = normalizeToArray(json?.VISISO?.TRANSACTIONS?.TRANSACTION);
        if (transactionsSettlements.length > 0) {
          settlementCronLogger.error(`Received settlement reject file with transactions: ${originalFileName}`);
          await sendEmailForUnexpectedError([
            {
              header: `Received settlement reject file with transactions: ${originalFileName}`,
              message: `${JSON.stringify(json)}`
            }
          ]);
          listOfFilesWithError.push(originalFileName);
          continue;
        } else {
          settlementCronLogger.info(`Received empty settlement reject file: ${originalFileName}`);
          continue;
        }
      }

      // 2.2 - Handle the settlement file
      const data = await handleSettlementFile(json, isRejected);
      if (data.failedCount != 0) {
        listOfFilesWithError.push(originalFileName);
        failedFilesWithErrors.push({
          fileName: originalFileName,
          failedReferenceNumbers: data.failedReferenceNumbers
        });
      }
      settlementCronLogger.info(
        `Result from settlement for file ${originalFileName}:\n${JSON.stringify(data, null, 2)}`
      );
    } catch (error) {
      settlementCronLogger.error(`Error handling transaction from file ${originalFileName}:`, error);
      listOfFilesWithError.push(originalFileName);
      failedFilesWithErrors.push({
        fileName: originalFileName,
        failedReferenceNumbers: []
      });
    }
  }

  // 3 - Send email for failed files with errors
  if (failedFilesWithErrors.length > 0) {
    settlementCronLogger.info(`Sending email for ${failedFilesWithErrors.length} failed files with errors...`);
    await sendEmailForFailedSettlementFiles(failedFilesWithErrors);
  } else {
    settlementCronLogger.info(`No failed files with errors to send email for.`);
  }

  // 4 - Upload failed files to OneDrive
  if (listOfFilesWithError.length > 0) {
    const jsonUploaded = await generateAndUploadFilteredJsonsToOneDrive(failedFilesWithErrors, parsedJSONs);
    if (!jsonUploaded) {
      settlementCronLogger.error('Failed to generate and upload filter JSON into OneDrive');
      const xmlUploaded = await uploadFailedXmlFilesToOneDrive(listOfFilesWithError);
      if (!xmlUploaded) {
        settlementCronLogger.error(`Failed to upload failed XML files to OneDrive ${listOfFilesWithError.join(', ')}`);
      }
    }
  } else {
    settlementCronLogger.info(`No failed files to upload to OneDrive.`);
  }

  // 5 - Upload all processed settlement files to Dropbox
  try {
    await handleSFTP('dropbox', {
      action: 'upload-selected-files',
      remoteDir: '/Incoming',
      localDir: 'downloads',
      fileNames: xmlFiles
    });
  } catch (error) {
    settlementCronLogger.error('Error uploading XML files to Dropbox', error);
    return;
  }

  // 6 - Delete all processed settled XML files from SFTP
  try {
    await handleSFTP('settlement', {
      action: 'delete-files',
      remoteDir: '/RYVYL_SETTLEMENTS',
      fileNames: xmlFiles
    });
  } catch (error) {
    settlementCronLogger.error('Error deleting XML files from SFTP', error);
    return;
  }

  // 7 - Delete all XML files from the downloads folder
  try {
    for (const file of xmlFiles) {
      await fs.unlink(`downloads/${file}`);
    }
  } catch (error) {
    settlementCronLogger.error('Error deleting XML files from downloads folder', error);
    return;
  }
}

// ___________------------- Unblock Not Resolved TXs -------------___________
/**
 * Unblocks and Resolves all tx that are not resolved after X days
 * Sends an email for the unblocked TXs
 */
async function UnblockNotResolvedTx() {
  const daysCheckedBack = resolvedTxCron.daysToCheckForCron;
  if (!daysCheckedBack) {
    resolveCronLogger.error('Days to check back not set');
    return;
  }

  // 1 - Find all TXs that are not resolved and unblock/resolve them and are older than X days
  // (10 working days -> ~15 days including weekends + 1 day buffer for holidays)
  const txs = await getUnresolvedTransactionsOlderThan(Number(daysCheckedBack));

  if (txs.length === 0) {
    resolveCronLogger.info('No transactions to resolve');
    return;
  }

  resolveCronLogger.info(`Found ${txs.length} transactions to resolve`);

  // 2 - Send email for all TXs that are not settled after X days
  const referenceNumbersOfTxs = txs.map((tx) => tx.retrievalReferenceNumber);
  await sendEmailForUnresolvedTX(referenceNumbersOfTxs, daysCheckedBack);

  // 3 - Resolves all TXs that are not settled and are older than X days
  for (const tx of txs) {
    resolveCronLogger.info(
      `Resolving ${returnDirectionTypeAsString(tx.transType.directionType)} Transaction: ${tx._id.toString()}, with retrievalReferenceNumber: ${tx.retrievalReferenceNumber}`
    );

    if (tx.blockId) {
      // 3.1 - Unblock amount
      const unblockAmount = await unblockAmountByBlockID(tx.blockId);
      if (!unblockAmount || unblockAmount.v_res !== tx.blockId || unblockAmount.v_res === 0) {
        // 3.2 - If error, update transaction resolvent with message
        resolveCronLogger.error(
          `Unblocking amount failed: ${JSON.stringify(unblockAmount)} for ${tx.retrievalReferenceNumber}`
        );
        await addTransactionMessage(
          tx._id.toString(),
          `Unblocking amount failed after ${daysCheckedBack} days passed: ${JSON.stringify(unblockAmount)}`,
          resolveCronLogger
        );
        continue;
      } else {
        // 3.3 - If success, update transaction resolvent with message and mark as resolved
        resolveCronLogger.info(
          `Unblocking amount successful: ${JSON.stringify(unblockAmount)} for ${tx.retrievalReferenceNumber}`
        );
        await updateTransactionReversalWithStatusAndResolventWithMessage(
          tx._id.toString(),
          true,
          TransactionStatusEnum.Reverted,
          true,
          TransactionResolventTypeEnum.ReversalCron,
          new Date().toISOString(),
          `Unblocking amount successful after ${daysCheckedBack} days passed: ${JSON.stringify(unblockAmount)}`,
          resolveCronLogger
        );
        continue;
      }
    } else {
      // 3.4 - Transactions with no block ID, mark as resolved
      resolveCronLogger.info(`Transaction has no block ID: ${tx.retrievalReferenceNumber}, marking as resolved`);
      await updateTransactionReversalWithStatusAndResolventWithMessage(
        tx._id.toString(),
        true,
        TransactionStatusEnum.Completed,
        true,
        TransactionResolventTypeEnum.ReversalCron,
        new Date().toISOString(),
        `Transaction has no block ID, marking as resolved after ${daysCheckedBack} days passed`,
        resolveCronLogger
      );
      continue;
    }
  }
}

// ___________------------- Send Email For Unresolved TXs -------------___________
/**
 * Handles sending an email for all TXs that are not resolved after X days
 * Sends the reference numbers of the TXs to the email service
 */
export async function sendEmailForUnresolvedTX(referenceNumbersOfTxs: (string | undefined)[], daysCheckedBack: string) {
  if (referenceNumbersOfTxs.length === 0) {
    resolveCronLogger.error('No referenceNumbersOfTxs to send email for');
    return;
  }

  try {
    const response = await makeBackendRequest({
      method: 'POST',
      maxBodyLength: Infinity,
      url: resolvedTxCron.emailServiceUrl + '/send-email-for-unsettled-transaction',
      data: {
        referenceNumbersOfTxs: referenceNumbersOfTxs,
        days: daysCheckedBack
      },
      headers: { 'Content-Type': 'application/json' }
    });

    if (response.status !== 200 && response.status !== 201) {
      resolveCronLogger.error(
        `Error sending email for txs retrieval reference numbers: ${referenceNumbersOfTxs}: ${JSON.stringify(response)}`
      );
      return;
    }

    resolveCronLogger.info(`Email sent successfully for txs reference numbers: ${referenceNumbersOfTxs}`);
  } catch (error) {
    resolveCronLogger.error(
      `Error sending email for txs retrieval reference numbers: ${referenceNumbersOfTxs}: ${error}`
    );
  }
}

// ___________------------- Send Email For Failed Settlement Files -------------___________
/**
 * Handles sending an email for all failed settlement files
 * @param failedFilesWithErrors the failed files with the errors
 */
export async function sendEmailForFailedSettlementFiles(
  failedFilesWithErrors: {
    fileName: string;
    failedReferenceNumbers: { rrn: string; error: string }[];
  }[]
) {
  if (failedFilesWithErrors.length === 0) {
    settlementCronLogger.error('No failedFilesWithErrors to send email for');
    return;
  }

  try {
    const response = await makeBackendRequest({
      method: 'POST',
      maxBodyLength: Infinity,
      url: resolvedTxCron.emailServiceUrl + '/send-email-for-failed-settlement-transactions',
      data: { failedFilesWithErrors },
      headers: { 'Content-Type': 'application/json' }
    });

    if (response.status !== 200 && response.status !== 201) {
      settlementCronLogger.error(
        `Error sending email for failed settlement files: ${JSON.stringify(failedFilesWithErrors)}: ${JSON.stringify(response)}`
      );
      return;
    }

    settlementCronLogger.info(
      `Email sent successfully for failed settlement files: ${JSON.stringify(failedFilesWithErrors)}`
    );
  } catch (error) {
    settlementCronLogger.error(
      `Error sending email for failed settlement files: ${JSON.stringify(failedFilesWithErrors)}: ${error}`
    );
  }
}

// ___________------------- Send Email For Unexpected Error -------------___________
/**
 * Handles sending an email for all TXs that are not resolved after X days
 * Sends the reference numbers of the TXs to the email service
 */
export async function sendEmailForUnexpectedError(topics: { header: string; message: string }[]) {
  if (topics.length === 0) {
    resolveCronLogger.error('No topics to send email for');
    return;
  }

  try {
    const response = await makeBackendRequest({
      method: 'POST',
      maxBodyLength: Infinity,
      url: resolvedTxCron.emailServiceUrl + '/unexpected-card-issuing-error-email',
      data: { topics },
      headers: { 'Content-Type': 'application/json' }
    });

    if (response.status !== 200 && response.status !== 201) {
      resolveCronLogger.error(
        `Error sending email for unexpected error: ${JSON.stringify(topics)}: ${JSON.stringify(response)}`
      );
      return;
    }

    resolveCronLogger.info(
      `Email sent successfully for ${topics.length} unexpected ${topics.length === 1 ? 'error' : 'errors'}`
    );
  } catch (error) {
    resolveCronLogger.error(`Error sending email for unexpected error: ${JSON.stringify(topics)}: ${error}`);
  }
}

// ___________------------- Upload JSON files to OneDrive -------------___________

/**
 * Interface for failed file with the retrieval reference numbers (rrn) and errors
 */
interface FailedFile {
  fileName: string;
  failedReferenceNumbers: {
    rrn: string;
    error: string;
  }[];
}

/**
 * Interface for parsed file with the JSON and the original file name
 */
interface ParsedFile {
  json: any;
  originalFileName: string;
}

/**
 * Generates and uploads filtered JSONs to OneDrive
 * @param failedFilesWithErrors Array of failed file names with the reference numbers and errors
 * @param parsedJSONs All XML settlement files parsed as JSONs
 * @returns true if successful, false otherwise
 */
async function generateAndUploadFilteredJsonsToOneDrive(
  failedFilesWithErrors: FailedFile[],
  parsedJSONs: ParsedFile[]
): Promise<boolean> {
  const uploadOptions: OneDriveUploadOptions[] = [];

  // 1 - Make sure there are failed files and parsed JSONs
  if (failedFilesWithErrors.length === 0 || parsedJSONs.length === 0) {
    settlementCronLogger.info('No failed files to generate filtered JSONs for');
    return true;
  }

  settlementCronLogger.info(`Generating filtered JSONs for ${failedFilesWithErrors.length} failed files...`);

  // 2 - 👇 Auto-generate folder name with the current date
  const oneDriveFolderPath = `Settlement_Errors/${new Date().toISOString().split('T')[0]}`;

  // 3 - Handle each file with the errors and retrieval reference numbers (RRN)
  try {
    for (const { fileName, failedReferenceNumbers } of failedFilesWithErrors) {
      // 3.1 - From all parsed files take those who have errors only!
      const matched = parsedJSONs.find((f) => f.originalFileName === fileName);
      if (!matched) continue;

      const json = matched.json;
      const transactions = json.VISISO.TRANSACTIONS.TRANSACTION;

      // 3.2 - Make a map of failed RRNs for faster lookup
      const failedRrnMap = new Map<string, string>();
      for (const { rrn, error } of failedReferenceNumbers) {
        failedRrnMap.set(rrn, error);
      }

      // 3.3 - Filter transactions that have failed RRNs
      const filtered = Array.isArray(transactions)
        ? transactions.filter((tx) => failedRrnMap.has(tx.VIS037_RRN))
        : failedRrnMap.has(transactions?.VIS037_RRN)
          ? [transactions]
          : [];

      if (filtered.length === 0) continue;

      // 3.4 - Enrich the filtered transactions with the error message
      const enriched = filtered.map((tx) => ({
        ...tx,
        _error: failedRrnMap.get(tx.VIS037_RRN) ?? null
      }));

      // 3.5 - Create the filtered JSON
      const filteredJson = {
        ...json,
        VISISO: {
          ...json.VISISO,
          TRANSACTIONS: {
            TRANSACTION: enriched
          },
          FILE_TRAILER: {
            ...json.VISISO.FILE_TRAILER,
            RECORD_NUMBER: enriched.length.toString()
          }
        }
      };

      // 3.6 - Prepare the upload options, convert the JSON to string and create the file name
      const fileContent = JSON.stringify(filteredJson, null, 2);
      const fileNameAsJson = fileName.replace(/\.xml$/i, '.json');

      // 3.7 - Add the upload options to the array
      uploadOptions.push({
        fileKey: fileNameAsJson,
        originalFilename: fileNameAsJson,
        filename: fileNameAsJson,
        content: Buffer.from(fileContent, 'utf-8'),
        mimeType: 'application/json',
        id: fileNameAsJson,
        folderName: oneDriveFolderPath
      });
    }

    // 4 - Make sure there are files to upload
    if (uploadOptions.length === 0) {
      settlementCronLogger.info('✅ No filtered JSONs to upload to OneDrive.');
      return true;
    }

    // 5 - Upload the filtered JSONs to OneDrive
    const results = await uploadFilesToOneDrive(uploadOptions, oneDriveFolderPath);
    settlementCronLogger.info(`✅ Uploaded ${results.length} filtered JSON file(s) to OneDrive.`);
    return true;
  } catch (err: any) {
    settlementCronLogger.error('❌ Failed to generate/upload filtered JSONs to OneDrive:', err);
    return false;
  }
}

// ___________------------- Upload failed XML files to OneDrive -------------___________
/**
 * Uploads failed XML files to OneDrive
 * @param listOfFilesWithError Array of failed file names
 * @returns true if successful, false otherwise
 */
async function uploadFailedXmlFilesToOneDrive(listOfFilesWithError: string[]): Promise<boolean> {
  if (listOfFilesWithError.length > 0) {
    settlementCronLogger.info(`Uploading ${listOfFilesWithError.length} failed files to OneDrive...`);

    // 1 - Prepare the upload options and create the folder path for current date
    const uploadOptions: OneDriveUploadOptions[] = [];
    const oneDriveFolderPath = `Settlement_Errors/${new Date().toISOString().split('T')[0]}`; // e.g., Settlement_Errors/2025-07-18

    // 2 - Add the upload options for each file that have errors
    for (const file of listOfFilesWithError) {
      const fullPath = path.resolve('downloads', file);
      const fileContent = await fs.readFile(fullPath);

      uploadOptions.push({
        fileKey: file,
        originalFilename: file,
        filename: file,
        content: fileContent,
        mimeType: 'application/xml',
        id: file,
        folderName: oneDriveFolderPath
      });
    }

    // 3 - Upload the whole XML files with errors to OneDrive
    try {
      const results = await uploadFilesToOneDrive(uploadOptions, oneDriveFolderPath);
      settlementCronLogger.info(`✅ Uploaded ${results.length} failed XML files to OneDrive.`);
      return true;
    } catch (err: any) {
      settlementCronLogger.error(`❌ Failed to upload error files to OneDrive`, err);
      return false;
    }
  } else {
    settlementCronLogger.info(`✅ No failed XML files to upload to OneDrive.`);
    return true;
  }
}
