import { Logger } from 'winston';
import {
  convertYYMMDDhhmmssToISO,
  formatAmount,
  formatDateTime,
  getCurrency,
  getCurrentUTCDateISO,
  getDecimals,
  getLast3Letters
} from '../utils/helpers';
import {
  TransactionInterfaceData,
  TransactionInterfaceNumbers,
  TransactionInterfaceNames,
  TransactionStatusEnum,
  TransactionResolventTypeEnum,
  TransactionUniqueCombinationFields
} from '../interfaces/transactionInterface';
import {
  addTransactionMessage,
  addTransactionMessageAndResolvent,
  getBiggestBlockId,
  getTransactionByApprovalCode,
  getTransactionByBlockId,
  getTransactionsByRetrievalReferenceNumber,
  getTransactionsByUniqueFieldsCombination,
  updateTransactionProcessingOutcomeApprovalCodeBlockIdStatusAndAddMessage,
  updateTransactionProcessingOutcomeBlockDataResolventAndAddMessage,
  updateTransactionProcessingOutcomeResponseWithMessageAndResolvent
} from '../queries/MongoTransactions';
import { codeCurrencyMap, currencyMap } from '../constants/currency';
import { blockAmountByIBAN } from '../queries/SqlProcedures';
import logger from '../utils/logger';
import { TransactionSettlementOrasysRecord } from '../interfaces/settlementInterfaces';
import { processingCodeMap, TransactionTypeData } from '../constants/transactionType';
import { findCardFeeByUniqueFields, findFXRateByCurrency } from '../queries/MongoFees';
import {
  CardFeeLocation,
  CustomerType,
  OperationType,
  ProductType,
  threeToTwoLetterCountryMap
} from '../constants/fees';
import { findCardByCardKey } from '../queries/MongoCards';
import { CardFee } from '../interfaces/feeTypes';
import { checkAvailableBalanceByIBAN } from './userService';

let latestBlockId = process.env.START_FROM_BLOCK_ID ? Number(process.env.START_FROM_BLOCK_ID) : 0;

export async function getLatestBlockId() {
  const blockId = await getBiggestBlockId();
  if (blockId && blockId > latestBlockId) {
    latestBlockId = blockId;
  }
  logger.info(`Latest block id set to: ${latestBlockId}`);
}

export async function checkIBANandCardID(
  IBAN: string | null | undefined,
  cardID: string | null | undefined
): Promise<{ message: string; IBAN?: string }> {
  if (!cardID && !IBAN) {
    return { message: 'No card ID and IBAN provided' };
  }

  if (!cardID && IBAN) {
    return { message: 'No card ID provided, using IBAN', IBAN };
  }

  if (cardID && !IBAN) {
    const card = await findCardByCardKey(cardID);
    if (!card) {
      return { message: 'Card ID not found in Mongo and IBAN not provided' };
    }
    return { message: `No IBAN provided, using card's IBAN: ${card.accNo}`, IBAN: card.accNo };
  }

  // If both are provided
  const card = await findCardByCardKey(cardID!);
  if (!card) {
    return { message: 'Card ID not found in Mongo' };
  }

  if (card.accNo !== IBAN) {
    return { message: 'Card ID does not match IBAN' };
  }

  return { message: '', IBAN };
}

// ____________________ Generate Fields ____________________

/**
 * Generates a Visa-compliant Field 38 Authorization Code.
 * The code should be unique as possible, but could have duplicates by docs.
 * Loops until a valid 6-character alphanumeric code is generated up to 8 times.
 * @param retrievalReferenceNumber The retrieval reference number
 * @param IBAN The IBAN
 * @param transmissionDateTime The transmission date time
 */
export async function generateValidAuthCode(
  retrievalReferenceNumber: string,
  IBAN: string, // Field 11
  transmissionDateTime: string // Field 7 (MMDDhhmmss)
): Promise<string | null> {
  let attempt = 0;
  let salt = '';
  let code = '';

  while (attempt < 8) {
    // Max 8 attempts
    const input = `${retrievalReferenceNumber}${transmissionDateTime}${IBAN}${salt}`;

    // Hash using DJB2-style algorithm
    let hash = 5381;
    for (let i = 0; i < input.length; i++) {
      hash = (hash << 5) + hash + input.charCodeAt(i);
    }

    // Generate 6-char base36 uppercase string
    code = Math.abs(hash).toString(36).toUpperCase().slice(0, 6).padEnd(6, '0');

    const isValid = isValidAuthCode(code);
    if (!isValid) {
      attempt++;
      salt = `ALT${attempt}`;
      continue;
    }

    const found = await getTransactionByApprovalCode(code);
    if (found && attempt != 8) {
      attempt++;
      salt = `ALT${attempt}`;
      continue;
    }

    return code;
  }

  return null;
}

// Validation rules (Visa-compliant)
const forbiddenRegexes = [
  /^0{6}$/, // "000000"
  /^0{5}[^0]$/, // "00000X"
  /^SVC.*/, // Starts with SVC
  /^.{5}X$/, // Ends with X
  /^.{5}\s$/, // Ends with space
  /^0{4}[NPY\s]$/, // "0000N", "0000P", "0000Y", or space
  /^\s{6}$/ // All spaces
];

export function isValidAuthCode(code: string): boolean {
  if (!/^[A-Z0-9]{6}$/.test(code)) return false;

  for (const rx of forbiddenRegexes) {
    if (rx.test(code)) return false;
  }

  return true;
}

/**
 * Generates Field 54 (Additional Amounts) for VisaNet ISO 8583
 * @param body - The ISO 8583 message body
 * @param balanceResult - The balance info containing amount and currency
 * @returns A 20-character Field 54 string
 */
export function generateField54(
  body: TransactionInterfaceNumbers,
  sign: 'C' | 'D',
  balanceResult: { op_availability: number; opCurrency: string }
): string {
  const processingCode = body['3'];
  const currencyAlpha = balanceResult.opCurrency;
  const currency = codeCurrencyMap[currencyAlpha]; // e.g., 'USD' → '840'

  if (!currency) {
    throw new Error(`Invalid currency: ${currencyAlpha}`);
  }

  const availableBalance = balanceResult.op_availability;

  const accountType = /^\d{6}$/.test(processingCode) ? processingCode.slice(0, 2) : '10';
  const amountType = '01'; // '01' = Available balance (standard)

  const entry = buildField54Entry({
    accountType,
    amountType,
    currency,
    sign,
    value: Math.abs(availableBalance)
  });

  return entry;
}

function buildField54Entry({
  accountType,
  amountType,
  currency,
  sign,
  value
}: {
  accountType: string;
  amountType: string;
  currency: string; // 3-digit ISO 4217
  sign: 'C' | 'D';
  value: number;
}): string {
  return `${accountType}${amountType}${currency}${sign}${formatAmount(value, currency)}`;
}

// ____________________ Block Data ____________________

type BlockProcedureData = {
  // ip_acc_no: string | undefined; // we pass this directly
  // ip_amount: number | undefined; // we pass this directly
  // ip_block_id: number | undefined; // we pass this directly
  // ip_card_no: string | undefined; // We know the mask number, which is not valid input for OraSys
  ip_terminal_id: string | undefined;
  // ip_authorization_code: string | undefined; //  we pass this directly
  ip_stan: string | undefined;
  ip_time: string;
  ip_transaction_amount: number | undefined;
  // ip_transaction_currency: string | undefined; // we pass this directly
  ip_place_transaction: string;
  ip_type_transaction: string;
};

export const mapFieldsFromTxToBlockID = (source: TransactionInterfaceData): BlockProcedureData => {
  return {
    // ip_amount: source.cardholderBillingAmount ? Number(source.cardholderBillingAmount) : undefined, // we pass the amount with fee directly
    // ip_card_no: cardNumber,
    ip_terminal_id: source.cardAcceptorTerminalId?.trim().slice(0, 8),
    // ip_authorization_code: source.approvalCode, // extract from result or settlement if available
    ip_stan: source.stan ? source.stan.slice(0, 6) : undefined,
    ip_time: formatDateTime(source.localTransactionTime ? source.localTransactionTime : ''),
    ip_transaction_amount: source.transactionAmount ? Number(source.transactionAmount) : undefined,
    // ip_transaction_currency: source.transactionCurrencyCode ? currencyMap[source.transactionCurrencyCode] : undefined, // we pass currency depending on which amount we use
    ip_place_transaction: source.cardAcceptorNameLocation.trim().slice(0, 40),
    ip_type_transaction: source.transType.codeOraSys
  };
};

// ____________________ Handle Amount ____________________

/**
 * Parses a VisaNet-style amount and adds it to an optional base amount.
 */
export function parseVisaNetAmount(rawAmount: string, currencyCode: string, baseAmount: number = 0): number {
  if (!/^\d{12}$/.test(rawAmount)) {
    throw new Error(`Invalid VisaNet amount format: ${rawAmount}`);
  }

  const decimalPlaces: number = getDecimals(currencyCode);
  const amountInt: number = parseInt(rawAmount, 10);
  const factor: number = Math.pow(10, decimalPlaces);

  const parsed = amountInt / factor;
  const result = baseAmount + parsed;

  return parseFloat(result.toFixed(decimalPlaces));
}

/**
 * Parses a VisaNet-style amount and adds it to an optional base amount.
 */
export function parseVisaAmount(
  rawAmount: string | undefined,
  currencyCode: string | undefined
): number | undefined | string {
  if (!rawAmount) return undefined;
  if (!currencyCode) return rawAmount;

  if (!/^\d{12}$/.test(rawAmount)) {
    throw new Error(`Invalid VisaNet amount format: ${rawAmount}`);
  }

  const decimalPlaces: number = getDecimals(currencyCode);
  const amountInt: number = parseInt(rawAmount, 10);
  const factor: number = Math.pow(10, decimalPlaces);

  const parsed = amountInt / factor;

  return parseFloat(parsed.toFixed(decimalPlaces));
}

/**
 * Converts Visa Field 10 Conversion Rate (Cardholder Billing) into a decimal number.
 *
 * @param {string } field10 - An 8-digit string or number in Visa Field 10 format.
 * @returns {number} - The actual conversion rate as a decimal.
 * @throws {Error} - If the input is not a valid 8-digit number.
 */
export function parseVisaConversionRate(field10: string): number {
  // Validate input
  if (!/^\d{8}$/.test(field10)) {
    throw new Error('Invalid Field 10 format. Must be exactly 8 digits.');
  }

  const decimalPos = parseInt(field10[0], 10); // first digit = decimal position
  const digits = field10.slice(1); // next 7 digits = rate digits

  // Insert decimal point at the correct position
  const rateWithDecimal = digits.padStart(decimalPos + 1, '0');
  const result = parseFloat(rateWithDecimal.slice(0, -decimalPos) + '.' + rateWithDecimal.slice(-decimalPos));

  return result;
}

interface VisaAmountResult {
  transactionAmount?: number;
  billingAmount?: number;
  expectedBilling?: number;
  conversionRate?: number;
  isValidConversion?: boolean;
  notes?: string[];
}

/**
 * Full VisaNet amount processor with currency & conversion logic
 */
export function processVisaAmountsWithValidation(message: Record<string, any>): VisaAmountResult {
  const rawTxn = message['4'];
  const rawBilling = message['6'];
  const rawRate = message['10'];
  const codeTxn = message['49'];
  const codeBilling = message['51'];

  const result: VisaAmountResult = { notes: [] };

  // Validate required transaction data
  if (!rawTxn) {
    result.notes?.push('Missing Field 4 (Transaction Amount)');
    return result;
  }
  if (!codeTxn) {
    result.notes?.push('Missing Field 49 (Transaction Currency)');
    return result;
  }

  try {
    result.transactionAmount = parseVisaNetAmount(rawTxn, codeTxn);
  } catch (e) {
    result.notes?.push(`Transaction amount error: ${(e as Error).message}`);
    return result;
  }

  // If billing amount and code exist, process cross-currency
  if (rawBilling && codeBilling) {
    try {
      result.billingAmount = parseVisaNetAmount(rawBilling, codeBilling);
    } catch (e) {
      result.notes?.push(`Billing amount error: ${(e as Error).message}`);
      return result;
    }

    const isSameCurrency = codeTxn === codeBilling;

    if (isSameCurrency) {
      result.notes?.push('Same currency — conversion rate check skipped.');
      result.isValidConversion = true;
    } else {
      if (!rawRate) {
        result.notes?.push('Missing Field 10 (Conversion Rate) for cross-currency transaction.');
        return result;
      }

      try {
        result.conversionRate = parseVisaConversionRate(rawRate);
        result.expectedBilling = parseFloat((result.transactionAmount * result.conversionRate).toFixed(2));
        result.isValidConversion = Math.abs(result.expectedBilling - result.billingAmount) <= 0.01;

        if (!result.isValidConversion) {
          result.notes?.push(`Mismatch: expected ${result.expectedBilling}, got ${result.billingAmount}`);
        }
      } catch (e) {
        result.notes?.push(`Conversion rate error: ${(e as Error).message}`);
        return result;
      }
    }
  } else {
    result.notes?.push('Billing data (Field 6/51) not present — skipping billing validation.');
  }

  return result;
}

type AmountWithFlexibleCurrency = {
  amount: number | undefined;
  currency: string; // could be "978" or "EUR"
};

export const normalizeCurrency = (currency: string): string => {
  // If it's already a 3-letter currency, return as is. Else map from code.
  return currency.length === 3 && /^[A-Z]+$/.test(currency) ? currency : currencyMap[currency] || currency;
};

// Checks if the balance is sufficient in the same currency
export const isSameCurrencyAndSufficient = (
  balance: AmountWithFlexibleCurrency,
  required: AmountWithFlexibleCurrency
): { isValid: boolean; message?: string } => {
  if (balance.amount === undefined || required.amount === undefined) {
    return { isValid: false, message: 'Invalid amount values' };
  }

  const balanceCurrency = normalizeCurrency(balance.currency);
  const requiredCurrency = normalizeCurrency(required.currency);

  if (balanceCurrency !== requiredCurrency) {
    return {
      isValid: false,
      message: 'Currency mismatch'
    };
  }

  if (balance.amount < required.amount) {
    return {
      isValid: false,
      message: 'Insufficient balance'
    };
  }

  return { isValid: true };
};

// ____________________ Block Amount With Retries ____________________

interface BlockAmountParams {
  IBAN: string;
  amount: number;
  currency: string;
  txId: string;
  logger: Logger;
  bodyOrTx: any;
  authCode: string;
  extraFields?: Record<string, any>;
}

/**
 * Blocks amount with retries, saves all data to Mongo and returns the result
 * @param IBAN the IBAN to block amount from
 * @param amount the total amount to block
 * @param currency the currency of the amount
 * @param txId the transaction id
 * @param logger the logger instance
 * @param bodyOrTx the transaction object
 * @param authCode the authorization code
 * @param extraFields extra fields to pass to the block amount function
 * @returns A promise that resolves to a BlockAmountResult object
 */
export async function attemptBlockAmountWithRetries({
  IBAN,
  amount,
  currency,
  txId,
  logger,
  bodyOrTx,
  authCode,
  extraFields = {}
}: BlockAmountParams) {
  let blockId = latestBlockId + 1;
  const blockDataSendToOraSys = {
    ...extraFields,
    ip_acc_no: IBAN,
    ip_amount: amount,
    ip_block_id: blockId,
    ip_transaction_currency: currency,
    ip_authorization_code: authCode
  };
  let blockAmount = await blockAmountByIBAN(blockDataSendToOraSys);
  if (!blockAmount.success && blockAmount.error?.message === 'Block ID is already in use') {
    for (let i = 1; i < 6; i++) {
      const newBlockId = blockId + i;
      latestBlockId = newBlockId;
      const found = await getTransactionByBlockId(newBlockId);
      if (found) continue;

      const newBlockAmount = await blockAmountByIBAN({
        ...extraFields,
        ip_acc_no: IBAN,
        ip_amount: amount,
        ip_block_id: newBlockId,
        ip_transaction_currency: currency,
        ip_authorization_code: authCode
      });

      if (newBlockAmount.success) {
        blockAmount = newBlockAmount;
        blockId = newBlockId;

        // Successful block, update transaction with new block id
        blockDataSendToOraSys.ip_block_id = newBlockId; // Update the block id in the object
        await updateTransactionProcessingOutcomeApprovalCodeBlockIdStatusAndAddMessage(
          txId,
          blockId,
          true,
          authCode,
          TransactionStatusEnum.Pending,
          `Blocked amount successfully: ${JSON.stringify(blockAmount.data)}`,
          logger,
          JSON.stringify(blockDataSendToOraSys)
        );

        return { success: true, blockAmount, blockId };
      }
    }
  } else if (blockAmount.success && blockAmount.data && !blockAmount.error) {
    latestBlockId = blockId;

    // Successful block, update transaction with new block id
    await updateTransactionProcessingOutcomeApprovalCodeBlockIdStatusAndAddMessage(
      txId,
      blockId,
      true,
      authCode,
      TransactionStatusEnum.Pending,
      `Blocked amount successfully: ${JSON.stringify(blockAmount.data)}`,
      logger,
      JSON.stringify(blockDataSendToOraSys)
    );

    return { success: true, blockAmount, blockId };
  }

  await updateTransactionProcessingOutcomeBlockDataResolventAndAddMessage(
    txId,
    JSON.stringify(blockDataSendToOraSys),
    true,
    TransactionResolventTypeEnum.Denied,
    getCurrentUTCDateISO(),
    `Error occurred while blocking amount ${JSON.stringify(blockAmount)}`,
    logger
  );
  logger.error(`Error occurred while blocking amount: ${JSON.stringify(bodyOrTx)}`);
  return { success: false, blockAmount, blockId };
}

// Build approved response for Financial Request
export function buildApprovedResponseFieldsFinancial(response: Record<string, any>, authCode: string, field54: string) {
  return {
    ...response,
    '38': authCode,
    '39': '000',
    '54': field54
  };
}

// ____________________ Balance Validation ____________________

/**
 * Checks if the balance is sufficient and add message to transaction if not
 * @param IBAN The IBAN of the account
 * @param amount The amount to check
 * @param txId The transaction id
 * @param transaction The transaction object to log
 * @param logger The logger instance
 * @returns A promise that resolves to a ValidationResult object
 */
export async function validateBalance(
  IBAN: string,
  amount: number,
  txId: string,
  transaction: any,
  logger: Logger
): Promise<{ success: true } | { success: false; statusCode: number; message: string }> {
  const balanceResult = await checkAvailableBalanceByIBAN(IBAN);

  if (balanceResult === null || balanceResult === undefined) {
    const errMsg = `Could not get the balance for IBAN: ${JSON.stringify(transaction)}`;
    logger.error(errMsg);
    await addTransactionMessage(txId, errMsg, logger);
    return { success: false, statusCode: 404, message: errMsg };
  }

  if (balanceResult < amount) {
    const errMsg = `IBAN has no sufficient funds - ${JSON.stringify(transaction)}, for balance: ${balanceResult}`;
    logger.error(errMsg);
    await addTransactionMessage(txId, errMsg, logger);
    return { success: false, statusCode: 400, message: errMsg };
  }

  return { success: true };
}

// ____________________ Deny Transactions ____________________

/**
 * Marks a transaction as resolved and denied and adds a message
 * @param txId The transaction id
 * @param message The message to add
 * @param logger The logger instance
 */
export async function denyTransactionWithMessage(txId: string, message: string, logger: Logger) {
  await addTransactionMessageAndResolvent(
    txId,
    message,
    true,
    TransactionResolventTypeEnum.Denied,
    getCurrentUTCDateISO(),
    logger
  );
}

/**
 * Marks a transaction as resolved and denied and adds a message + response
 * @param txId The transaction id
 * @param message The message to add
 * @param response The response to add
 * @param logger The logger instance
 */
export async function denyTransactionWithMessageAndResponse(
  txId: string,
  message: string,
  response: any,
  logger: Logger
) {
  await updateTransactionProcessingOutcomeResponseWithMessageAndResolvent(
    txId,
    response,
    message,
    true,
    TransactionResolventTypeEnum.Denied,
    getCurrentUTCDateISO(),
    logger
  );
}

// ____________________ Verify Transaction Existence ____________________

/**
 * Verifies that there is only one transaction with that reference number
 * @param referenceNumber The reference number to check
 * @returns The transaction if it exists, null otherwise
 */
export async function verifyOneTransactionWithThatReferenceNumber(
  referenceNumber: string
): Promise<{ isOne: boolean; transaction: any }> {
  const existingTx = await getTransactionsByRetrievalReferenceNumber(referenceNumber);
  if (existingTx) {
    if (existingTx.length > 1) {
      return { isOne: false, transaction: null };
    } else {
      return { isOne: true, transaction: existingTx[0] };
    }
  }
  return { isOne: false, transaction: null };
}

/**
 * Verifies that there is only one transaction with that reference number and that it is unique
 * @param referenceNumber The reference number to check
 * @returns The transaction if it exists, null otherwise
 */
// @Todo: Make sure the unique fields are always present and correct!
export async function verifyUniqueTransaction(
  transactionUniqueCombinationFields: TransactionUniqueCombinationFields
): Promise<{ isOne: boolean; transaction: any }> {
  const existingTx = await getTransactionsByUniqueFieldsCombination(transactionUniqueCombinationFields);
  if (existingTx) {
    if (existingTx.length > 1) {
      return { isOne: false, transaction: null };
    } else {
      return { isOne: true, transaction: existingTx[0] };
    }
  }
  return { isOne: false, transaction: null };
}

// ____________________ Transaction Type Classification ____________________

/**
 * Maps ISO 8583 Field 3 (Processing Code) to OraSys Transaction details object
 * @param processingCode - The 6-digit processing code from the ISO message
 * @returns OraSys Transaction details object, or undefined if not mapped
 */
export function mapProcessingCodeToOraSysDetails(processingCode: string): TransactionTypeData | undefined {
  if (!processingCode || !/^\d{6}$/.test(processingCode)) {
    throw new Error('Invalid processing code format');
  }

  const txType = processingCode.slice(0, 2);

  return processingCodeMap[txType];
}

// ____________________ Fee Calculation ____________________

/**
 * Finds and applies the FX fee to the transaction amount
 * @param txFeeCode - The currency code from the transaction
 * @param billingAmountEuro - The billing amount in EURO
 * @returns The FX fee amount
 */
export async function ApplyFXFee(txFeeCode: string, billingAmountEuro: number): Promise<number> {
  const mappedCurrency = currencyMap[txFeeCode];
  if (!mappedCurrency || mappedCurrency === 'EUR') {
    throw new Error(`Currency not mapped: ${txFeeCode} or equal to EUR`);
  }

  const fxRate = await findFXRateByCurrency(mappedCurrency);
  if (!fxRate) {
    throw new Error(`FX rate not found for currency: ${mappedCurrency}`);
  }

  const percentageFee = fxRate.percentage / 100;
  const flatFee = fxRate.flatFee;

  return parseFloat((billingAmountEuro * percentageFee + flatFee).toFixed(2));
}

/**
 * MOCK: Finds and applies the normal fee to the transaction amount
 * @param billingAmountEuro - The billing amount in EURO
 * @returns The normal fee amount
 */
export async function ApplyNormalFee(
  billingAmountEuro: number,
  operationType: OperationType,
  customerType: CustomerType,
  productType: ProductType,
  location?: CardFeeLocation
): Promise<{ feeAmount: number; cardFee: CardFee } | null> {
  const fee = await findCardFeeByUniqueFields(operationType, customerType, productType, location);
  if (!fee) return null;

  const percentageFee = fee.percentageFee / 100;
  const flatFee = fee.fixedFee;

  return { feeAmount: parseFloat((billingAmountEuro * percentageFee + flatFee).toFixed(2)), cardFee: fee };
}

/**
 * Calculates the fee amount based on the billing amount and the fee object
 * @param billingAmountEuro The amount in EURO
 * @param fee The fee object
 * @returns The fee amount
 */
export function calculateFeeAmount(billingAmountEuro: number, fee: CardFee): number {
  const percentageFee = fee.percentageFee / 100;
  const flatFee = fee.fixedFee;
  return parseFloat((billingAmountEuro * percentageFee + flatFee).toFixed(2));
}

/**
 * Returns the direction type as string
 * @param directionType The direction type
 * @returns The direction type as string
 */
export function returnDirectionTypeAsString(directionType: 'D' | 'C' | 'N'): string {
  return directionType === 'D' ? 'Debit' : directionType === 'C' ? 'Credit' : 'Inquiry';
}

/**
 * Returns the 2 digits location from the field 43
 * @param field43 The field 43 from the ISO message
 * @returns The 2 digits location
 */
export function get2digitsLocationFromField43(field43: string): CardFeeLocation {
  const country = getLast3Letters(field43);
  return threeToTwoLetterCountryMap[country];
}

// ____________________ Map Transaction Data To Schema ____________________

/**
 * Converts numeric fields to named fields for transaction
 * @param data The numeric fields from IT Card
 * @param IBAN We also have optional field IBAN if 102 field is not passed, but we find the IBAN from the cardKey!
 * @returns The named fields conversion data
 */
export function convertStringFieldsToNamedInterface(
  data: TransactionInterfaceNumbers,
  IBAN?: string
): TransactionInterfaceNames {
  const result: TransactionInterfaceNames = {
    messageTypeIndicator: data['0'] || '',
    primaryAccountNumber: data['2'],
    processingCode: data['3'],
    transactionAmount: parseVisaAmount(data['4'], data['49']) || data['4'],
    reconciliationAmount: parseVisaAmount(data['5'], data['50']) || data['5'],
    cardholderBillingAmount: parseVisaAmount(data['6'], data['51']) || data['6'],
    transmissionDateTime: data['7'],
    reconciliationConversionRate: data['9'],
    cardholderBillingConversionRate: data['10'],
    stan: data['11'],
    localTransactionTime: convertYYMMDDhhmmssToISO(data['12']) || data['12'],
    expirationDate: data['14'],
    reconciliationDate: data['15'],
    conversionDate: data['16'],
    merchantCategoryCode: data['18'],
    acquiringInstitutionCountryCode: data['19'],
    pointOfServiceDataCode: data['22'],
    cardSequenceNumber: data['23'],
    functionCode: data['24'],
    messageReasonCode: data['25'],
    cardAcceptorBusinessCode: data['26'],
    amountsOriginal: data['30'],
    acquiringInstitutionId: data['32'],
    forwardingInstitutionId: data['33'],
    acceptanceEnvironmentData: data['34'],
    track2Data: data['35'],
    track3Data: data['36'],
    retrievalReferenceNumber: data['37'],
    approvalCode: data['38'],
    actionCode: data['39'],
    cardAcceptorTerminalId: data['41'],
    cardAcceptorIdCode: data['42'],
    cardAcceptorNameLocation: data['43'],
    additionalResponseData: data['44'],
    track1Data: data['45'],
    amountsFeesISO: data['46'],
    transactionCurrencyCode: getCurrency(data['49']),
    reconciliationCurrencyCode: getCurrency(data['50']),
    cardholderBillingCurrencyCode: getCurrency(data['51']),
    pinData: data['52'],
    amountsAdditional: data['54'],
    iccData: data['55'],
    originalDataElements: data['56'],
    authorizationLifeCycleCode: data['57'],
    authorizingAgentInstitutionIdCode: data['58'],
    reservedPrivate: data['62'],
    dataRecord: data['72'],
    transactionDestinationInstitutionIdCode: data['93'],
    transactionDestinationOriginatorIdCode: data['94'],
    accountIdentification1: data['102'] || IBAN,
    reservedForPrivateUse: data['123'],
    cardId: data['124'] && data['124']['09'] ? data['124']['09'] : undefined
  };

  return result;
}

export function mapToOraSysDataRecordFromCreditTx(
  tx: TransactionInterfaceData,
  approvalCode: string,
  cardNumber: string,
  cardHolder: string,
  IBAN: string
): TransactionSettlementOrasysRecord {
  if (
    tx.cardholderBillingAmount &&
    Number(tx.cardholderBillingAmount) <= (tx.fees.taxAmount || 0) + (tx.fees.taxAmountFx || 0)
  ) {
    throw new Error('Cardholder billing amount is less or equal to the fees');
  }
  const date = new Date(); // Get the UTC current time

  return {
    transDate: tx.localTransactionTime ? new Date(tx.localTransactionTime) : date,
    schDate: date,
    valueDate: date,
    transType: tx.transType.codeOraSys,
    descriptor: tx.fees?.taxNarrative ?? 'UNKNOWN DESCRIPTOR',
    mcc: tx.merchantCategoryCode ?? '',
    region: tx.cardAcceptorNameLocation ? get2digitsLocationFromField43(tx.cardAcceptorNameLocation) : 'N/A',
    authCode: approvalCode,
    cardNo: cardNumber,
    accountNo: IBAN,
    cardHolder: cardHolder,
    transAmount: tx.transactionAmount ? Number(tx.transactionAmount) : 0,
    transCodval: tx.transactionCurrencyCode ?? '',
    cardAmount: tx.cardholderBillingAmount ? Number(tx.cardholderBillingAmount) : 0,
    cardCodval: tx.cardholderBillingCurrencyCode ?? '',
    setlAmount: tx.cardholderBillingAmount ? Number(tx.cardholderBillingAmount) : 0,
    setlCodval: tx.cardholderBillingCurrencyCode ?? '',
    taxAmount: tx.fees.taxAmount ?? 0,
    taxAmountFx: tx.fees.taxAmountFx ?? 0,
    idBlock: undefined,
    idRowSch: 0 // Not used by IL, set to 0
  };
}

// ____________________ Unique Fields ____________________
export function mapIsoNumbersToUniqueFields(
  isoData: TransactionInterfaceNumbers
): TransactionUniqueCombinationFields | string {
  // Map the fields from numeric keys to meaningful names
  const mapped: TransactionUniqueCombinationFields = {
    retrievalReferenceNumber: isoData[37] || '',
    merchantCategoryCode: isoData[18] || '',
    cardAcceptorTerminalId: isoData[41] || '',
    cardAcceptorIdCode: isoData[42] || ''
  };

  // Collect missing or empty fields for error reporting
  const missingFields = Object.entries(mapped)
    .filter(([_, value]) => !value || value.trim() === '')
    .map(([key]) => key);

  if (missingFields.length > 0) {
    return `Missing required ISO8583 fields for mapping: ${missingFields.join(', ')}`;
  }

  return mapped;
}
