import {
  createCardFee,
  getAllCardFees,
  findCard<PERSON>ees,
  findCardFeeByUniqueFields,
  createFXRate,
  findFXRateByCurrencyAndClientID,
  getAllFXRates,
  findFXRates
} from '../queries/MongoFees';
import { cardFeeLogger } from '../utils/logger';
import { getErrorMessage } from '../utils/helpers';
import { CardFee, FXRate } from '../interfaces/feeTypes';

const feeServiceLogger = cardFeeLogger.child({ service: 'CardFeeService' });

/**
 * Creates a new card fee after verifying no duplicate exists
 * @param feeData - Complete card fee data to be added
 * @returns The newly created card fee document or error msg
 */
export async function addCardFee(feeData: CardFee): Promise<{ data?: any; error?: string }> {
  try {
    const existingFee = await findCardFeeByUniqueFields(
      feeData.operationType,
      feeData.customerType,
      feeData.productType,
      feeData.location,
      feeData.modifier
    );

    if (existingFee) {
      return {
        error: `Fee with matching unique fields already exists: ${feeData.feeCode}, ${feeData.customerType}, ${feeData.productType}, ${feeData.location}, ${feeData.modifier}`
      };
    }

    const newFee = await createCardFee(feeData);
    if (!newFee) {
      return {
        error: 'Failed to create card fee in the database'
      };
    }

    feeServiceLogger.info(`Created new card fee: ${feeData.displayDescriptor}`);
    return {
      data: newFee
    };
  } catch (err) {
    feeServiceLogger.error('Failed to add card fee:', err);
    return {
      error: 'Unexpected error while adding card fee'
    };
  }
}

/**
 * Retrieves card fees matching optional filter criteria
 * @param filter - Optional filter criteria for searching fees
 * @returns Array of matching card fee documents
 * @throws Error if retrieval operation fails
 */
export async function getCardFees(filter?: Partial<CardFee>) {
  try {
    if (filter) {
      return await findCardFees(filter);
    } else {
      return await getAllCardFees();
    }
  } catch (error) {
    feeServiceLogger.error('Error retrieving card fees:', error);
    throw error;
  }
}

/**
 * Creates card fees in the database
 * @param validRecords - Array of valid card fees to be created (schema validate with joi)
 * @returns Array of created card fees and array of errors
 */
export async function createCardFeesInDatabase(validRecords: CardFee[]): Promise<{
  created: any[];
  errors: string[];
}> {
  const created: any[] = [];
  const errors: string[] = [];

  for (const fee of validRecords) {
    try {
      const { data: createdFee, error: errMsg } = await addCardFee(fee);
      if (errMsg) {
        errors.push(errMsg);
      } else {
        created.push(createdFee);
      }
    } catch (error) {
      errors.push(getErrorMessage(error));
    }
  }

  return { created, errors };
}

// __________ FX Rate Fees __________

/**
 * Creates a FX rate after verifying no duplicate exists
 * @param feeData - Complete FX rate data to be added
 * @returns The newly created FX rate document or error msg
 */
export async function addFXRate(rateData: FXRate): Promise<{ data?: any; error?: string }> {
  try {
    const existingRate = await findFXRateByCurrencyAndClientID(rateData.currency, rateData.clientID);
    if (existingRate) {
      return {
        error: `FX rate for ${rateData.currency} and client ${rateData.clientID} already exists`
      };
    }

    const newRate = await createFXRate(rateData);
    if (!newRate) {
      return {
        error: 'Failed to create FX rate in the database'
      };
    }

    feeServiceLogger.info(`Created new FX rate: ${rateData.currency} for client ${rateData.clientID}`);
    return {
      data: newRate
    };
  } catch (err) {
    feeServiceLogger.error('Failed to add FX rate:', err);
    return {
      error: 'Unexpected error while adding FX rate'
    };
  }
}

/**
 * Retrieves card fees matching optional filter criteria
 * @param filter - Optional filter criteria for searching fees
 * @returns Array of matching card fee documents
 * @throws Error if retrieval operation fails
 */
export async function getFXRates(filter?: Partial<FXRate>) {
  try {
    if (filter) {
      return await findFXRates(filter);
    } else {
      return await getAllFXRates();
    }
  } catch (error) {
    feeServiceLogger.error('Failed to get FX rates:', error);
    throw error;
  }
}
