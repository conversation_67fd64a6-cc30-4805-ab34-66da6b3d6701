import { getUserBalanceAndAvailabilityByIBAN, getUserBalanceIIInfo } from '../queries/SqlProcedures';

/**
 * Checks the available balance for a given IBAN
 * @param IBAN - The IBAN to check
 * @returns The available balance or null if not found
 */
export async function checkAvailableBalanceByIBAN(IBAN: string) {
  const result = await getUserBalanceAndAvailabilityByIBAN(IBAN);
  if (result && result.op_availability) {
    return result.op_availability;
  } else {
    return null;
  }
}

/**
 * Checks the total balance for a given IBAN
 * @param IBAN - The IBAN to check
 * @returns The total balance or null if not found
 */
export async function checkTotalBalanceByIBAN(IBAN: string) {
  const result = await getUserBalanceAndAvailabilityByIBAN(IBAN);
  if (result && result.op_account_balance) {
    return result.op_account_balance;
  } else {
    return null;
  }
}

/**
 * Checks the total and available balance for a given IBAN
 * @param IBAN - The IBAN to check
 * @returns The total and available balance or null if not found
 */
export async function checkTotalAndAvailableBalanceByIBAN(IBAN: string) {
  const result = await getUserBalanceAndAvailabilityByIBAN(IBAN);
  if (result && result.op_account_balance && result.op_availability) {
    return result;
  } else {
    return null;
  }
}

/**
 * Checks the balance details for a given IBAN
 * @param IBAN - The IBAN to check
 * @returns The balance info or null if not found
 */
export async function checkBalanceInfoByIBAN(IBAN: string) {
  const result = await getUserBalanceIIInfo(IBAN);
  if (result) {
    return result;
  } else {
    return null;
  }
}
