import axios, { Method } from 'axios';
import { ONEDRIVE } from '../config';
import { oneDriveLogger } from '../utils/logger';
import {
  OneDriveFile,
  OneDriveFileResult,
  TokenResponse,
  OneDriveUploadResult,
  OneDriveUploadOptions
} from '../interfaces/oneDriveInterface';
import { AppError } from '@submodules/ryvyl-commons/classes/AppError';

/**
 * Cache for access token to avoid unnecessary requests
 */
let tokenCache: {
  token: string;
  expiresAt: number;
} | null = null;

/**
 * Get Microsoft Graph API access token using client credentials flow
 */
export async function getAccessToken(): Promise<string> {
  try {
    // Check if we have a valid cached token
    if (tokenCache && tokenCache.expiresAt > Date.now()) {
      return tokenCache.token;
    }

    const tokenUrl = `https://login.microsoftonline.com/${ONEDRIVE.TENANT_ID}/oauth2/v2.0/token`;

    const params = new URLSearchParams();
    params.append('client_id', ONEDRIVE.CLIENT_ID);
    params.append('client_secret', ONEDRIVE.CLIENT_SECRET);
    params.append('scope', ONEDRIVE.SCOPE);
    params.append('grant_type', 'client_credentials');

    const response = await axios.post<TokenResponse>(tokenUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    const { access_token, expires_in } = response.data;
    // Cache the token with a 5-minute buffer before expiration
    tokenCache = {
      token: access_token,
      expiresAt: Date.now() + (expires_in - 300) * 1000
    };

    return access_token;
  } catch (error: any) {
    if (error.response) {
      oneDriveLogger.error(
        `❌ OneDrive token request failed with status ${error.response.status}: ${JSON.stringify(error.response.data)}`
      );
    } else {
      oneDriveLogger.error(`❌ Failed to get OneDrive access token: ${error.message}`);
    }
    throw new Error(`Failed to authenticate with OneDrive: ${error.message}`);
  }
}

/**
 * Get Microsoft Graph API request options
 */
function getGraphApiOptions(method: Method, path: string, accessToken: string, data?: any) {
  return {
    method: method.toUpperCase(),
    url: `https://graph.microsoft.com/v1.0${path}`,
    headers: {
      Authorization: `Bearer ${accessToken}`
      // 'Content-Type': 'application/json'
    },
    ...(method.toUpperCase() !== 'GET' && data && { data })
  };
}

/**
 * Download a file from OneDrive
 * @param file OneDrive file metadata
 * @returns File content as Buffer and Base64
 */
export async function downloadOneDriveFile(file: OneDriveFile): Promise<OneDriveFileResult> {
  try {
    const downloadUrl = file['@microsoft.graph.downloadUrl'];

    if (!downloadUrl) {
      throw new Error('Download URL not available for file');
    }

    const response = await axios.get(downloadUrl, {
      responseType: 'arraybuffer'
    });

    const content = Buffer.from(response.data);
    const base64Content = content.toString('base64');
    const mimeType = file.file?.mimeType || 'application/octet-stream';

    return {
      filename: file.name,
      content,
      base64Content,
      mimeType,
      size: content.length
    };
  } catch (error: any) {
    oneDriveLogger.error(`❌ Error downloading OneDrive file ${file.name}: ${error.message}`);
    throw new Error(`Failed to download OneDrive file: ${error.message}`);
  }
}

/**
 * Get file data from One Drive
 * @param filename The name of the file to search for
 * @param accessToken The access token for authentication
 * @param submissionId The submission ID to use for folder-specific search
 * @returns OneDrive file metadata if found
 */
async function getFileData(
  filename: string,
  accessToken: string,
  submissionId: string,
  folderName: string
): Promise<OneDriveFile | null> {
  const searchPath = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/root:/${folderName}/${submissionId}/${filename}`;
  const result = await axios(getGraphApiOptions('GET', searchPath, accessToken));
  const files = result.data;
  return files;
}

/**
 * Get file from OneDrive by filename and return as Base64
 * @param filename The name of the file to retrieve
 * @param submissionId The submission ID to use for folder-specific search
 * @returns File content as Base64 string and metadata
 */
export async function getOneDriveFileAsBase64(
  filename: string,
  submissionId: string,
  folderName: string
): Promise<OneDriveFileResult | null> {
  try {
    let file: OneDriveFile | null = null;
    const accessToken = await getAccessToken();
    file = await getFileData(filename, accessToken, submissionId, folderName);
    if (!file) {
      return null;
    }

    const fileResult = await downloadOneDriveFile(file);
    return fileResult;
  } catch (error: any) {
    oneDriveLogger.error(`❌ Failed to get OneDrive file ${filename}: ${error.message}`);
    throw error;
  }
}

/**
 * Delete a folder from OneDrive
 * @param folderPath The path to the folder to delete
 * @param accessToken The access token for authentication
 * @returns Promise<boolean> - true if folder was deleted successfully
 */
export async function deleteFolderFromOneDrive(folderPath: string): Promise<boolean> {
  let accessToken: string | null = null;
  try {
    accessToken = await getAccessToken();
  } catch (error: any) {
    oneDriveLogger.error(`❌ Failed to delete folder, because of getAccessToken: ${error.message}`);
    return false;
  }

  if (!accessToken) {
    return false;
  }

  try {
    const getFolderPath: string = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/root:/${folderPath}`;

    // First, try to get the folder to ensure it exists and get its ID
    const folderResult: any = await axios(getGraphApiOptions('GET', getFolderPath, accessToken));
    const folderId = folderResult.data.id;
    if (!folderId) {
      return true;
    }

    // Delete the folder using its ID
    const deleteFolderPath: string = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/items/${folderId}`;

    await axios({
      ...getGraphApiOptions('DELETE', deleteFolderPath, accessToken),
      headers: {
        ...getGraphApiOptions('DELETE', deleteFolderPath, accessToken).headers
      }
    });

    oneDriveLogger.info(`✅ Successfully deleted OneDrive folder: ${folderPath}`);
    return true;
  } catch (error: any) {
    let errorMessage = 'Unknown error occurred';
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // Server responded with a non-2xx code
        if (error.response.status === 404) {
          oneDriveLogger.warn(`⚠️ Folder not found in OneDrive: ${folderPath}`);
          return true; // Folder doesn't exist, consider it "deleted"
        }
        errorMessage = error.response.data?.error?.message || error.message;
      } else {
        // Something went wrong setting up the request
        errorMessage = error.message;
      }
    } else if (error instanceof Error) {
      errorMessage = error.message;
    }

    oneDriveLogger.error(`❌ Error deleting OneDrive folder ${folderPath}: ${errorMessage}`);
    throw new Error(`Failed to delete OneDrive folder: ${errorMessage}`);
  }
}

/**
 * Create a folder in OneDrive if it doesn't exist
 * @param folderPath The path to the folder to create
 * @param accessToken The access token for authentication
 * @returns The folder metadata
 */
export async function createFolderIfNotExists(folderPath: string, accessToken: string): Promise<any> {
  try {
    // Split the path into individual folder names
    const folderNames = folderPath.split('/').filter((name) => name.length > 0);
    let currentPath = '';
    let parentFolderId = null;

    // Create each folder level recursively
    for (const folderName of folderNames) {
      currentPath = currentPath ? `${currentPath}/${folderName}` : folderName;

      try {
        // Try to get the current folder level

        const getFolderPath: string = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/root:/${currentPath}`;
        const result: any = await axios(getGraphApiOptions('GET', getFolderPath, accessToken));
        parentFolderId = result.data.id;
      } catch (error: any) {
        // If folder doesn't exist, create it
        if (error.response?.status === 404) {
          const createFolderPath: string = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/root:/${currentPath}`;
          const folderData = {
            name: folderName,
            folder: {},
            '@microsoft.graph.conflictBehavior': 'rename'
          };

          const result: any = await axios({
            ...getGraphApiOptions('PUT', createFolderPath, accessToken, folderData),
            headers: {
              ...getGraphApiOptions('PUT', createFolderPath, accessToken, folderData).headers,
              'Content-Type': 'application/json'
            }
          });
          oneDriveLogger.info(`✅ Created OneDrive folder: ${currentPath}`);
        } else {
          throw error;
        }
      }
    }

    // Return the final folder metadata
    if (parentFolderId) {
      const finalFolderPath = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/items/${parentFolderId}`;
      const result = await axios(getGraphApiOptions('GET', finalFolderPath, accessToken));
      return result.data;
    }

    throw new Error('Failed to create folder structure');
  } catch (error: any) {
    let errorMessage = '';
    if (axios.isAxiosError(error)) {
      if (error.response) {
        // Server responded with a non-2xx code
        errorMessage = error.response.data?.error?.message || error.message;
      } else {
        // Something went wrong setting up the request
        errorMessage = error.message;
      }
    } else {
      errorMessage = error.message;
    }
    throw new Error(`❌ Error creating OneDrive folder ${folderPath}: ${errorMessage}`);
  }
}
/**
 * Upload a file to OneDrive
 * @param options Upload options including filename, content, and target location
 * @returns Upload result with file metadata
 */
export async function uploadFileToOneDrive(
  options: OneDriveUploadOptions,
  folderPath: string
): Promise<OneDriveUploadResult> {
  try {
    const { filename, content, mimeType = 'application/octet-stream', id, folderName } = options;

    const accessToken = await getAccessToken();
    await createFolderIfNotExists(folderPath, accessToken);

    // Prepare the upload path
    const uploadPath = `/users/${ONEDRIVE.ONEDRIVE_EMAIL}/drive/root:/${folderPath}/${filename}:/content`;

    // Convert content to Buffer if it's a string
    const fileBuffer = typeof content === 'string' ? Buffer.from(content, 'utf-8') : content;

    // Upload the file
    const result = await axios({
      ...getGraphApiOptions('PUT', uploadPath, accessToken, fileBuffer),
      headers: {
        ...getGraphApiOptions('PUT', uploadPath, accessToken, fileBuffer).headers,
        'Content-Type': mimeType
      }
    });

    const uploadedFile = result.data;

    oneDriveLogger.info(`✅ Successfully uploaded file to OneDrive: ${filename} size: (${fileBuffer.length} bytes)`);

    return {
      id: uploadedFile.id,
      name: uploadedFile.name,
      size: uploadedFile.size,
      webUrl: uploadedFile.webUrl,
      downloadUrl: uploadedFile['@microsoft.graph.downloadUrl'],
      mimeType: uploadedFile.file?.mimeType || mimeType
    };
  } catch (error: any) {
    oneDriveLogger.error(`❌ Error uploading file to OneDrive ${options.filename}: ${error.message}`);
    throw new Error(`Failed to upload file to OneDrive: ${error.message}`);
  }
}

/**
 * Upload multiple files to OneDrive
 * @param files Array of upload options
 * @returns Array of upload results with success/failure status
 */
export async function uploadFilesToOneDrive(
  files: OneDriveUploadOptions[],
  folderPath: string
): Promise<(OneDriveUploadResult | undefined)[]> {
  if (!files || files.length === 0) {
    return [];
  }
  const accessToken = await getAccessToken();
  // We need to create folder before uploading files, because after that when we run promise.all,
  // we can have duplicate folders, because of the delay on a creation of a folder.
  await createFolderIfNotExists(folderPath, accessToken);
  await new Promise((resolve) => setTimeout(resolve, 200));

  const uploadPromises = files.map(async (file) => {
    try {
      const result = await uploadFileToOneDrive(file, folderPath);
      return {
        status: 'fulfilled' as const,
        value: result
      };
    } catch (error: any) {
      return {
        status: 'rejected' as const,
        reason: error.message,
        originalFilename: file.originalFilename
      };
    }
  });

  const results = await Promise.all(uploadPromises);
  const failedResults = results.filter((result) => result.status === 'rejected');
  if (failedResults.length > 0) {
    const userErrorMessage = `Failed to upload file${failedResults.length > 1 ? 's' : ''} : ${failedResults.map((result) => result.originalFilename).join(', ')}`;
    const agentErrorMessage = JSON.stringify(failedResults, null, 2);

    throw new AppError({
      userMessage: userErrorMessage,
      agentMessage: agentErrorMessage,
      statusCode: 500
    });
  }

  return results.map((result, index) => {
    return result.value;
  });
}
