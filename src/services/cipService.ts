import {
  InsertTxWithApplyingFee<PERSON>PInterfaceFields,
  SimpleTransaction,
  InsertTxCIPInterfaceFields,
  TransactionInterfaceData,
  TransactionInterfaceNames
} from '../interfaces/transactionInterface';
import { findDebitAccountByIBAN, findDebitAccountClientId } from '../queries/MongoDebit';
import { findCardByCardKey } from '../queries/MongoCards';
import { findClientByClientId } from '../queries/MongoClients';
import { addTransactionMessage } from '../queries/MongoTransactions';
import { unblockAmountByBlockID } from '../queries/SqlProcedures';
import { sendEmailForUnexpectedError } from './cronService';
import { denyTransactionWithMessage } from './transactionService';
import { ServiceResult } from '../utils/helpers';

// ___________ Map ___________ Transaction ___________ Data ___________
/**
 * From the transactions in rage and maps data properly to the response needed to be send for CIP
 */
export const mapToSimpleTransaction = (tx: TransactionInterfaceData): SimpleTransaction => {
  const {
    processingCode,
    transactionAmount,
    cardholderBillingAmount,
    transmissionDateTime,
    reconciliationConversionRate,
    stan,
    expirationDate,
    retrievalReferenceNumber,
    cardAcceptorTerminalId,
    cardAcceptorIdCode,
    cardAcceptorNameLocation,
    cardId,
    transactionCurrencyCode,
    cardholderBillingCurrencyCode,
    accountIdentification1,
    initialBodyData
  } = tx;

  return {
    transactionProcessingCode: processingCode || '',
    transactionAmount: Number(transactionAmount || 0),
    billingAmount: Number(cardholderBillingAmount || 0),
    transactionDatetime: transmissionDateTime || '',
    conversionRate: reconciliationConversionRate || '',
    traceNumber: stan || '',
    cardExpiryDate: expirationDate || '',
    retrievalReference: retrievalReferenceNumber || '',
    terminalId: cardAcceptorTerminalId || '',
    merchantId: cardAcceptorIdCode || '',
    merchantLocation: typeof cardAcceptorNameLocation === 'string' ? cardAcceptorNameLocation : '',
    cardId: cardId || '',
    currencyTransaction: transactionCurrencyCode || '',
    currencyBilling: cardholderBillingCurrencyCode || '',
    accountIdentification: accountIdentification1 || '',
    customFields: {
      transactionInfo: initialBodyData?.transactionInfo || '',
      paypalReference: initialBodyData?.paypalReference || ''
    },
    extendedData: {
      authorizationLevel: initialBodyData?.authorizationLevel || '',
      authorizationMethod: initialBodyData?.authorizationMethod || '',
      productCode: initialBodyData?.productCode || '',
      deviceId: initialBodyData?.deviceId || '',
      channelId: initialBodyData?.channelId || '',
      riskScore: initialBodyData?.riskScore || '',
      retryCount: initialBodyData?.retryCount || '',
      trackingId: initialBodyData?.trackingId || ''
    },
    resolvent: {
      type: tx.resolvent.type,
      resolved: tx.resolvent.resolved,
      date: tx.resolvent.date
    },
    processingOutcome: {
      approved: tx?.processingOutcome?.approved || false
    },
    fees: {
      taxType: tx.fees.taxType || '',
      taxAmount: tx.fees.taxAmount || 0,
      taxAmountFx: tx.fees.taxAmountFx || 0,
      taxNarrative: tx.fees.taxNarrative || ''
    },
    externalToken: initialBodyData?.externalToken || '',
    transType: tx.transType,
    transStatus: tx.transStatus
  };
};

export const mapToSimpleTransactions = (transactions: TransactionInterfaceData[]): SimpleTransaction[] => {
  return transactions.map(mapToSimpleTransaction);
};

// Map received data to transaction
export function convertCIPFieldsToNamedInterface(data: InsertTxCIPInterfaceFields): TransactionInterfaceNames {
  const result: TransactionInterfaceNames = {
    messageTypeIndicator: 'CIP',
    transactionAmount: data.transactionAmount,
    reconciliationAmount: data.transactionAmount,
    cardholderBillingAmount: data.transactionAmount,
    transmissionDateTime: new Date().toISOString(),
    localTransactionTime: new Date().toISOString(),
    reconciliationDate: new Date().toISOString(),
    transactionCurrencyCode: data.currency,
    reconciliationCurrencyCode: data.currency,
    cardholderBillingCurrencyCode: data.currency,
    retrievalReferenceNumber: data.retrievalReferenceNumber.toString(),
    merchantCategoryCode: data.mcc,
    accountIdentification1: data.IBAN,
    cardId: data.cardID
  };

  return result;
}

// Map received applying fee data
export function convertCIPFieldsToNamedInterfaceApplyFee(
  data: InsertTxWithApplyingFeeCIPInterfaceFields
): TransactionInterfaceNames {
  const result: TransactionInterfaceNames = {
    messageTypeIndicator: 'CIP Apply Fee',
    transactionAmount: data.transactionAmount || 0,
    reconciliationAmount: data.transactionAmount || 0,
    cardholderBillingAmount: data.transactionAmount || 0,
    transmissionDateTime: new Date().toISOString(), // TODO: Timezone check on all dates
    localTransactionTime: new Date().toISOString(),
    reconciliationDate: new Date().toISOString(),
    transactionCurrencyCode: 'EUR',
    reconciliationCurrencyCode: 'EUR',
    cardholderBillingCurrencyCode: 'EUR',
    retrievalReferenceNumber: data.retrievalReferenceNumber.toString(),
    merchantCategoryCode: data.mcc || '4829', // 4829 is the default mcc for applying fees
    accountIdentification1: data.IBAN,
    cardId: data.cardID
  };

  return result;
}

// Get card and account from Mongo
export async function getCardAndAccount(
  cardID: string,
  IBAN: string
): Promise<{ card: any; cardAccount: any; matched: boolean }> {
  const card = await findCardByCardKey(cardID);
  const cardAccount = await findDebitAccountByIBAN(IBAN);

  if (cardAccount && card && cardAccount.accountNumber === card.accNo) {
    return { card, cardAccount, matched: true };
  }

  return { card, cardAccount, matched: false };
}

// _______ Validate _______ Client _______ ID _______ or _______ IBAN _______

export const isClientOrAccountRegistered = async (clientCode: string, iban: string): Promise<boolean> => {
  const clientFound = await findClientByClientId(clientCode);
  const accountByClientCode = await findDebitAccountClientId(clientCode);
  const ibanFound = await findDebitAccountByIBAN(iban);

  return Boolean(clientFound || ibanFound || accountByClientCode);
};

// _______ Unblock _______ Amount _______ on _______ Failure _______

/**
 * Context object for the handleOraSysInsertFailure function
 * @param txId The transaction id
 * @param blockId The block id
 * @param oraSysData The data sent to OraSys
 * @param oraSysResult The result from OraSys
 * @param txInfo The transaction object
 * @param response The express response
 * @param CIPLogger The logger instance
 */
type OraSysInsertFailureContext = {
  txId: string;
  blockId: number;
  oraSysData: any;
  oraSysResult: any;
  txInfo: any;
  response: any;
  CIPLogger: any;
};

/**
 * Handles the unblocking of the amount in case the OraSys insert fails and sends email if unblocking fails
 * @param context The context object containing all needed data
 * @returns void
 */
export async function handleOraSysInsertFailure(context: OraSysInsertFailureContext) {
  const { txId, blockId, oraSysData, oraSysResult, txInfo, response, CIPLogger } = context;

  // 1 - Build error message & log
  const errMessage = `Error inserting OraSys transaction data: ${JSON.stringify(oraSysData)}, Error: ${oraSysResult.message}`;
  CIPLogger.error(errMessage);

  // 2 - Add message to the transaction
  await addTransactionMessage(txId, errMessage, CIPLogger);

  // 3 - Respond with 400
  response.status(400).send(errMessage);

  // 4️ - Try to unblock the amount
  let resultMessage: string;
  const unblockAmount = await unblockAmountByBlockID(blockId);
  if (!unblockAmount || unblockAmount.v_res !== blockId || unblockAmount.v_res === 0) {
    // 4.1 Unblock failed
    resultMessage = `Unblocking amount failed for CIP TX that failed to insert into OraSys: ${JSON.stringify(unblockAmount)} for ${txInfo.retrievalReferenceNumber}`;
    CIPLogger.error(resultMessage);

    await addTransactionMessage(txId, resultMessage, CIPLogger);

    // 4.2 Send email alert
    await sendEmailForUnexpectedError([
      {
        header: 'CIP TX failed to insert into OraSys and unblocking amount failed',
        message: `${JSON.stringify(txInfo)}`
      }
    ]);
  } else {
    // 4.3 Unblock succeeded
    resultMessage = `Unblocking amount successful for CIP TX that failed to insert into OraSys: ${JSON.stringify(unblockAmount)} for ${txInfo.retrievalReferenceNumber}`;
    CIPLogger.info(resultMessage);

    // 4.4 Deny transaction with message
    await denyTransactionWithMessage(txId, resultMessage, CIPLogger);
  }

  return; // Explicitly end the function
}

// _______ Get _______ Card _______ and _______ Account _______
/**
 * Gets the card and account from Mongo and checks if they match (only account if cardID is not provided)
 * @param txId The transaction id
 * @param txData The transaction data
 * @param CIPLogger The logger instance
 * @returns ServiceResult with card/account data or error message
 */
export async function getCardAndAccountOrFail({
  txId,
  txData,
  CIPLogger
}: {
  txId: string;
  txData: any;
  CIPLogger: any;
}): Promise<ServiceResult<{ card: any | null; cardAccount: any }>> {
  let cardFound = null;
  let cardAccountFound = null;

  if (txData.cardID) {
    const { card, cardAccount, matched } = await getCardAndAccount(txData.cardID, txData.IBAN);
    cardFound = card;
    cardAccountFound = cardAccount;

    if (!cardFound || !cardAccountFound) {
      const errMsg = `Error finding card or account: ${JSON.stringify(txData)}`;
      CIPLogger.error(errMsg);
      await denyTransactionWithMessage(txId, errMsg, CIPLogger);
      return { success: false, error: errMsg };
    }

    if (!matched) {
      const errMsg = `IBAN does not match card account number in Mongo: ${JSON.stringify(txData)}`;
      CIPLogger.error(errMsg);
      await denyTransactionWithMessage(txId, errMsg, CIPLogger);
      return { success: false, error: errMsg };
    }
  } else {
    cardAccountFound = await findDebitAccountByIBAN(txData.IBAN);

    if (!cardAccountFound) {
      const errMsg = `Error finding account: ${JSON.stringify(txData)}`;
      CIPLogger.error(errMsg);
      await denyTransactionWithMessage(txId, errMsg, CIPLogger);
      return { success: false, error: errMsg };
    }
  }

  return { success: true, data: { card: cardFound, cardAccount: cardAccountFound } };
}
