import XLSX from 'xlsx';

import { CardFee } from '../interfaces/feeTypes';
import { cardFeeSchema } from '../schemaValidations/FeeSchemaValidation';
import { cardFeeLogger } from '../utils/logger';
import { cleanStringValue, getErrorMessage, isValidStringValue } from '../utils/helpers';

const excelLogger = cardFeeLogger.child({ service: 'ExcelParserService' });

// Mapping from CardFee property names to Excel column names
const propertyToExcelColumn: Record<string, string> = {
  GLAccountName: 'GL Account Name',
  feeCode: 'Fee Code',
  description: 'Description',
  operationType: 'Operation Type',
  customerType: 'Customer Type',
  productType: 'Product Type',
  location: 'Location',
  modifier: 'Modifier',
  trigger: 'Trigger',
  loopOnCard: 'Loop On Card',
  displayDescriptor: 'Display (Fee Narrative)',
  criteria: 'Criteria',
  type: 'Fee Type',
  currency: 'Fee Currency',
  fixedFee: 'Fixed Fee',
  percentageFee: '% Fee',
  supportsFXFee: 'FX'
};

/**
 * Parses an Excel file to CardFee objects.
 * @param filePath - The path to the Excel file. The supported files are .xls, .xlsx, .csv, and any alternative CSV mime type.
 * @returns An object containing the valid and invalid records.
 * @throws An error if the file cannot be parsed.
 * @example
 * // Example output:
 * {
 *   "validRecords": [
 *     {
 *       "GLAccountName": "Card Issuing Fees",
 *       "feeCode": "01",
 *       ...
 *       "currency": "EUR",
 *       "fixedFee": 0.1,
 *       "percentageFee": 0,
 *       "supportsFXFee": false
 *     },
 *     {
 *       "GLAccountName": "Card Issuing Fees",
 *       "feeCode": "01",
 *       ...
 *       "currency": "EUR",
 *       "fixedFee": 0.11,
 *       "percentageFee": 0,
 *       "supportsFXFee": false
 *     }
 *   ],
 *   "invalidRecords": [
 *     {
 *       "row": 8,
 *       "errors": [
 *         "Display (Fee Narrative) column is empty or invalid"
 *       ]
 *     },
 *     {
 *       "row": 53,
 *       "errors": [
 *         "Fee Code column is empty or invalid"
 *       ]
 *     }
 *   ]
 * }
 */
export async function parseExcelToCardFees(filePath: string): Promise<{
  validRecords: CardFee[];
  invalidRecords: { row: number; errors: string[] }[];
}> {
  const validRecords: CardFee[] = [];
  const invalidRecords: { row: number; errors: string[] }[] = [];

  try {
    const workbook = XLSX.readFile(filePath, {
      cellDates: true,
      cellNF: false,
      cellText: false,
      raw: false,
      dateNF: 'yyyy-mm-dd'
    });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const rawData = XLSX.utils.sheet_to_json(worksheet);
    rawData.forEach((row: any, index: number) => {
      const rowNumber = index + 2; // +2 because Excel is 1-indexed and we have headers

      if (!row || Object.keys(row).length === 0) {
        return;
      }
      const cleanedRow: any = {};
      Object.keys(row).forEach((key) => {
        cleanedRow[key] = cleanStringValue(row[key]);
      });

      try {
        // Map Excel columns to CardFee interface
        const cardFee: CardFee = {
          GLAccountName: cleanStringValue(row['GL Account Name']) || undefined,
          feeCode: cleanStringValue(row['Fee Code']),
          description: cleanStringValue(row['Description']),
          operationType: cleanStringValue(row['Operation Type']),
          customerType: cleanStringValue(row['Customer Type']),
          productType: cleanStringValue(row['Product Type']),
          location: isValidStringValue(row['Location']) ? cleanStringValue(row['Location']) : undefined,
          modifier: cleanStringValue(row['Modifier']) || undefined,
          trigger: cleanStringValue(row['Trigger']),
          loopOnCard: isValidStringValue(row['Loop On Card']),
          displayDescriptor: cleanStringValue(row['Display (Fee Narrative)']),
          criteria: cleanStringValue(row['Criteria']) || undefined,
          type: cleanStringValue(row['Fee Type']),
          currency: cleanStringValue(row['Fee Currency']),
          fixedFee: parseFloat(row['Fixed Fee']) || 0,
          percentageFee: parseFloat(row['% Fee']) || 0,
          supportsFXFee: isValidStringValue(row['FX'])
        };

        const validation = cardFeeSchema.validate(cardFee, { abortEarly: false });
        if (validation.error) {
          // Map Joi errors to user-friendly messages and log technical details
          const errorMessages = validation.error.details.map((d) => {
            const prop = d.path[0] as string;
            const excelCol = propertyToExcelColumn[prop] || prop;
            return `${excelCol} column is empty or invalid`;
          });

          // Log technical details for developers
          validation.error.details.forEach((detail) => {
            const prop = detail.path[0] as string;
            excelLogger.error({
              error: detail.message,
              property: prop,
              row: rowNumber
            });
          });

          invalidRecords.push({
            row: rowNumber,
            errors: errorMessages
          });
        } else {
          validRecords.push(cardFee);
        }
      } catch (error) {
        const errDetails = getErrorMessage(error);
        invalidRecords.push({
          row: rowNumber,
          errors: [`Parsing error: ${errDetails}`]
        });
      }
    });

    excelLogger.info(
      `Parsed ${validRecords.length} valid records and ${invalidRecords.length} invalid records from Excel file`
    );
    return { validRecords, invalidRecords };
  } catch (error) {
    const errDetails = getErrorMessage(error);
    excelLogger.error(`Failed to parse Excel file: ${errDetails}`);
    throw new Error(`Failed to parse Excel file: ${JSON.stringify(invalidRecords)}`);
  }
}

/**
 * Checks if a property is active using the cell value , for example, to find out if FX fees are active we need to have value which is different than the N/A and its variations.
 * @param cellValue - The cell value to check
 * @returns True if the cell value is not empty or not N/A and other variations, false otherwise.
 */
function isFeePropertyActive(cellValue: any): boolean {
  return (
    cellValue && cellValue != 'N/A' && cellValue != 'NA' && cellValue != 'n/a' && cellValue != 'na' && cellValue != ''
  );
}
