import winston from 'winston';
import {
  extractFieldsForResponse1110,
  extractFieldsForResponse1130,
  extractFieldsForResponse1210,
  extractFieldsForResponse1230,
  extractFieldsForResponse1430
} from '../constants/responseFields';
import { TransactionResolventTypeEnum } from '../interfaces/transactionInterface';
import { verifyOneTransactionWithThatReferenceNumber } from './transactionService';

/**
 * Validation result type used to validate the transaction request
 */
type ValidationResult = {
  valid: boolean;
  error?: string;
  errorCode?: string;
  response?: any;
};

/**
 * Transaction context type used to handle the transaction request
 */
interface TransactionContext {
  body: any;
  logger: winston.Logger;
  messageType: 'request' | 'advice' | 'reversal';
}

/**
 * Main validator function with specific validators
 * @param context - Transaction context containing body, logger, and message type
 * @returns Validation result with valid flag, error, error code, and response
 */
export function validateTransaction(context: TransactionContext): ValidationResult {
  const { body, logger, messageType } = context;

  // Check message code exists
  if (!body['0']) {
    logger.error(`Missing message code: ${JSON.stringify(body)}`);
    return { valid: false, error: 'Missing message code' };
  }

  const messageCode = Number(body['0']);

  // Validate message code based on transaction type
  const codeValidation = validateAndBuildResponse(messageCode, body, messageType);
  if (!codeValidation.valid) {
    logger.error(`${codeValidation.error ?? 'Invalid request'}: ${body['0']}. Body: ${JSON.stringify(body)}`);
    return codeValidation;
  }

  return { valid: true, response: codeValidation.response };
}

/**
 * Validates message code and builds the appropriate response in one step
 * @param code - Message code
 * @param body - Request body
 * @param type - Transaction type
 * @returns Validation result with valid flag, response object, and any error details
 */
function validateAndBuildResponse(code: number, body: any, type: 'request' | 'advice' | 'reversal'): ValidationResult {
  // Request message validation and response building
  if (type === 'request') {
    if (code === 1100 || code === 1101) {
      const response = extractFieldsForResponse1110(body);
      response['0'] = '1110';
      response['39'] = '100'; // Default: Denied, do not honor

      return { valid: true, response };
    }

    if (code === 1200 || code === 1201) {
      const response = extractFieldsForResponse1210(body);
      response['0'] = '1210';
      response['39'] = '100'; // Default: Denied, do not honor

      return { valid: true, response };
    }

    return { valid: false, error: 'Invalid request message code' };
  }

  // Advice message validation and response building
  if (type === 'advice') {
    if (code === 1120 || code === 1121) {
      const response = extractFieldsForResponse1130(body);
      response['0'] = '1130';
      return { valid: true, response };
    }

    if (code === 1220 || code === 1221) {
      const response = extractFieldsForResponse1230(body);
      response['0'] = '1230';

      return { valid: true, response };
    }

    return { valid: false, error: 'Invalid advice message code' };
  }

  // Reversal message validation and response building
  if (type === 'reversal') {
    if (code === 1420 || code === 1421) {
      const response = extractFieldsForResponse1430(body);
      response['0'] = '1430';
      response['39'] = '484'; // Reversal default

      return { valid: true, response };
    }

    return {
      valid: false,
      error: 'Invalid reversal message code',
      errorCode: '484'
    };
  }

  return { valid: false, error: 'Unknown transaction type' };
}

/**
 * Validates the existence of a transaction based on the message type
 * @param referenceNumber - The reference number of the transaction
 * @param messageType - The type of message (request, advice, reversal or return)
 * @param logger - The logger instance
 * @returns A promise that resolves to a ValidationResult object
 */
export async function validateExistingTransaction(
  referenceNumber: string,
  messageType: string,
  logger: winston.Logger
): Promise<ValidationResult> {
  // 1 - Get the existing transaction (should be one by reversal number and for reversal should be unique)
  const txResult = await verifyOneTransactionWithThatReferenceNumber(referenceNumber);

  // 1.1 - If there are multiple transactions, return error
  if (!txResult.isOne) {
    logger.error(`Multiple transactions found for reference number: ${referenceNumber}`);
    return { valid: false, error: 'Multiple transactions found' };
  }
  const existingTx = txResult.transaction;

  // 2 - If request - make sure transaction does not exist
  if (messageType === 'request') {
    if (existingTx) {
      logger.error(`Transaction with reference number ${referenceNumber} already exists`);
      return {
        valid: false,
        error: 'Transaction already exists',
        errorCode: '193' // Denied, scheduled transaction already exists
      };
    }
    return { valid: true };
  }

  // 3 - If return - make sure transaction with same RRN exists and is not an inquiry or credit
  if (messageType === 'return') {
    if (existingTx && existingTx.transType.directionType !== 'D') {
      logger.error(`Cannot return an inquiry or credit: ${referenceNumber}`);
      return {
        valid: false,
        error: 'Cannot return an inquiry or credit',
        errorCode: '100' // Denied, do not honor
      };
    }
    return { valid: true, response: existingTx };
  }

  // 4 - If advice - make sure transaction does not exist
  if (messageType === 'advice') {
    if (existingTx) {
      logger.error(`Transaction with reference number ${referenceNumber} already exists`);
      return { valid: false, error: 'Transaction already exists' };
    }
    return { valid: true };
  }

  // 5 - If reversal - make sure transaction exists, is unique and is not an inquiry
  if (messageType === 'reversal') {
    if (!existingTx) {
      logger.error(`Original transaction not found for reversal with reference number: ${referenceNumber}`);
      return {
        valid: false,
        error: 'Original transaction not found',
        errorCode: '481' // Reversal, original transaction not found
      };
    } else if (existingTx.transType.directionType === 'N') {
      logger.error(`Cannot reverse an inquiry: ${referenceNumber}`);
      return {
        valid: false,
        error: 'Cannot reverse an inquiry',
        errorCode: '484' // Reversal, original transaction not approved
      };
    } else if (existingTx.transType.codeOraSys === '5') {
      logger.error(`Cannot reverse a return: ${referenceNumber}`);
      return {
        valid: false,
        error: 'Cannot reverse a return',
        errorCode: '484' // Reversal, original transaction not approved
      };
    }
    return { valid: true, response: existingTx };
  }

  return { valid: false, error: 'Unknown validation type' };
}

/**
 * Validates the details of a reversal transaction
 * @param existingTx - The existing transaction
 * @param body - The request body
 * @param logger - The logger instance
 * @returns A promise that resolves to a ValidationResult object
 */
export function validateReversalDetails(existingTx: any, body: any, logger: winston.Logger): ValidationResult {
  // Check transaction amount and time match
  if (existingTx.initialBodyData['4'] !== body['4'] || existingTx.initialBodyData['7'] !== body['7']) {
    logger.error(`Transaction amount or time mismatch for reversal`);
    return { valid: false, error: 'Amount or time mismatch' };
  }

  // Validate IBAN match
  if (existingTx.initialBodyData['102'] !== body['102']) {
    logger.error(`Transaction IBAN mismatch for reversal`);
    return { valid: false, error: 'IBAN mismatch' };
  }

  // Check resolvent status
  if (existingTx.resolvent.resolved && existingTx.resolvent.type === TransactionResolventTypeEnum.Reversal) {
    logger.error(`Transaction already reversed for reversal`);
    return { valid: false, error: 'Transaction already reversed' };
  } else if (existingTx.resolvent.resolved) {
    logger.error(`Transaction already resolved for reversal`);
    return { valid: false, error: 'Transaction already resolved' };
  }

  return { valid: true };
}
