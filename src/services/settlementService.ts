import { settlementLogger } from '../utils/logger';
import {
  addTransactionMessage,
  updateTransactionSettlementWithMessage,
  updateTransactionSettlementWithStatusAndResolventWithMessage
} from '../queries/MongoTransactions';
import { insertOraSysTransaction } from '../queries/SqlQueries';
import {
  InsertTxWithApplyingFeeCIPInterfaceFields,
  InsertTxCIPInterfaceFields,
  TransactionInterfaceData,
  TransactionResolventTypeEnum,
  TransactionStatusEnum,
  TransactionUniqueCombinationFields
} from '../interfaces/transactionInterface';
import { TransactionSettlementOrasysRecord, VisSettlementTransaction } from '../interfaces/settlementInterfaces';
import { TransactionTypeData } from '../constants/transactionType';
import { getCurrentUTCDateISO, normalizeToArray } from '../utils/helpers';
import { getCardAndAccount } from './cipService';
import { currencyMap } from '../constants/currency';
import { get2digitsLocationFromField43, verifyUniqueTransaction } from './transactionService';
import { unblockAmountByBlockID } from '../queries/SqlProcedures';

// ____________________ Handle Settlement ____________________

/**
 * Handles the settlement data received from VisaNet.
 * @param settlementData The settlement data received from VisaNet.
 * @returns An object containing the number of successful and failed transactions, and an array of failed reference numbers.
 */
export async function handleSettlementFile(
  settlementData: any,
  isRejectedFile: boolean
): Promise<{
  successfulCount: number;
  failedCount: number;
  failedReferenceNumbers: { rrn: string; error: string }[];
}> {
  // 1 - Get all transactions from the settlement data
  const transactionsSettlements = normalizeToArray(settlementData?.VISISO?.TRANSACTIONS?.TRANSACTION);
  if (transactionsSettlements.length === 0) {
    settlementLogger.warn('No transactions found for file: ' + settlementData?.VISISO?.FILE_HEADER?.FILE_NAME);
    return {
      successfulCount: 0,
      failedCount: 0,
      failedReferenceNumbers: []
    };
  }

  // 2 - Initialize counters
  let successfulCount = 0;
  let failedCount = 0;
  const failedReferenceNumbers: { rrn: string; error: string }[] = [];

  // 3 - Handle each transaction
  for (const txSettlement of transactionsSettlements) {
    try {
      let result;
      if (isRejectedFile) {
        result = await handleSingleTxSettlementRejection(txSettlement);
      } else {
        result = await handleSingleTxSettlement(txSettlement);
      }
      if (result?.success) {
        successfulCount++;
      } else {
        failedCount++;
        failedReferenceNumbers.push({
          rrn: txSettlement.VIS037_RRN ?? 'UNKNOWN_RRN',
          error: result?.error ?? 'Unknown error during settlement'
        });
      }
    } catch (error) {
      failedCount++;
      failedReferenceNumbers.push({
        rrn: txSettlement.VIS037_RRN ?? 'UNKNOWN_RRN',
        error: (error as Error).message || 'Unhandled exception'
      });
      settlementLogger.error(`Unhandled error during settlement for ${txSettlement.VIS037_RRN}`, error);
    }
  }

  return {
    successfulCount,
    failedCount,
    failedReferenceNumbers
  };
}

/**
 * Handles a single settlement transaction. Find transaction in our database and write settlement. Execute write query in OraSys.
 * @param txSettlement The settlement transaction data.
 * @returns An object containing a success flag.
 */
async function handleSingleTxSettlement(
  txSettlement: VisSettlementTransaction
): Promise<{ success: boolean; error?: string }> {
  // 1 - Validate transaction
  const validationResult = await validateSettlementTransaction(txSettlement, settlementLogger);
  if (!validationResult.success) {
    return validationResult; // Already contains { success: false, error }
  }

  const ourTx = validationResult.ourTx;

  // 2 -  Check if TX is resolved & not Credit OCT 29
  const isOCT = ourTx.resolvent.type === TransactionResolventTypeEnum.Credit;
  if ((ourTx.resolvent.resolved && !isOCT) || (ourTx.settlement.settled && ourTx.resolvent.resolved)) {
    settlementLogger.error(
      `Transaction already resolved: ${ourTx.retrievalReferenceNumber}, Saved new settlement data in messages!`
    );
    addTransactionMessage(
      ourTx._id.toString(),
      `Received settlement for resolved Tx: ${JSON.stringify(txSettlement)}`,
      settlementLogger
    );
    return { success: false, error: 'Transaction already resolved' };
  }

  // 3 - Get card and account details
  const { card, cardAccount } = await getCardAndAccount(ourTx.cardId, ourTx.accountIdentification1);
  if (!card || !cardAccount) {
    const msg = `Error finding card or account: ${JSON.stringify(ourTx)}`;
    settlementLogger.error(msg);
    await addTransactionMessage(ourTx._id.toString(), msg, settlementLogger);
    return { success: false, error: msg };
  }

  // 4 - Map data and send it to OraSys
  const mappedData = mapToSettlementRecordCombined(txSettlement, ourTx, card.cardMask, cardAccount.accountHolder);

  // 4.1 - If OCT, update transaction settlement with message if the same
  if (isOCT) {
    const msg = 'OCT transaction received and passed settlement check';
    await updateTransactionSettlementWithMessage(
      ourTx._id.toString(),
      true,
      txSettlement,
      mappedData,
      msg,
      settlementLogger
    );
    settlementLogger.info(msg + ' for ' + ourTx.retrievalReferenceNumber);
    return { success: true };
  }

  const settlementResult = await insertOraSysTransaction(mappedData);
  if (!settlementResult.success) {
    // 4.2 - If error, add message to transaction
    const msg = `Error inserting settlement data: ${settlementResult.message}`;
    settlementLogger.error(
      `Error inserting settlement data: ${JSON.stringify(mappedData)}, Error: ${settlementResult.message}`
    );
    // Update transaction settlement with message on error
    await updateTransactionSettlementWithMessage(
      ourTx._id.toString(),
      false,
      txSettlement,
      mappedData,
      `Error: ${settlementResult.message}, received settlement: ${JSON.stringify(txSettlement)}`,
      settlementLogger
    );
    return { success: false, error: msg };
  }

  // 4.3 - If success, update transaction resolvent with message and mark as resolved
  settlementLogger.info(`Settlement data inserted: ${JSON.stringify(mappedData)}`);
  // Update settlement data in our database
  await updateTransactionSettlementWithStatusAndResolventWithMessage(
    ourTx._id.toString(),
    true,
    TransactionStatusEnum.Completed,
    true,
    TransactionResolventTypeEnum.Settlement,
    getCurrentUTCDateISO(),
    'Settlement successful',
    settlementLogger,
    txSettlement,
    mappedData
  );

  return { success: true };
}

/**
 * Handles a single settlement rejected transaction. Find transaction in our database and mark as declined. Unblock amount if blocked.
 * @param txSettlement The settlement transaction data.
 * @returns An object containing a success flag.
 */
async function handleSingleTxSettlementRejection(
  txSettlement: VisSettlementTransaction
): Promise<{ success: boolean; error?: string }> {
  // @Todo: Identify problem for rejection and make new tx if needed!

  // 1 - Validate transaction
  const validationResult = await validateSettlementTransaction(txSettlement, settlementLogger);
  if (!validationResult.success) {
    return validationResult; // Already contains { success: false, error }
  }

  const ourTx = validationResult.ourTx;

  // 2 -  Check if TX is resolved, including Credit OCT 29
  if (ourTx.resolvent.resolved) {
    settlementLogger.error(
      `Transaction already resolved: ${ourTx.retrievalReferenceNumber}, Saved new settlement data in messages!`
    );
    addTransactionMessage(
      ourTx._id.toString(),
      `Received settlement rejection for resolved Tx: ${JSON.stringify(txSettlement)}`,
      settlementLogger
    );
    return { success: false, error: 'Transaction already resolved' };
  }

  // 3 - Reject and resolve transaction
  const dateISO = getCurrentUTCDateISO();
  if (ourTx.blockId) {
    // 3.1 - Unblock amount
    const unblockAmount = await unblockAmountByBlockID(ourTx.blockId);
    if (!unblockAmount || unblockAmount.v_res !== ourTx.blockId || unblockAmount.v_res === 0) {
      // 3.2 - If error, update transaction settlement with message
      settlementLogger.error(
        `Unblocking amount failed from settlement rejection: ${JSON.stringify(unblockAmount)} for ${ourTx.retrievalReferenceNumber}`
      );
      await addTransactionMessage(
        ourTx._id.toString(),
        `Unblocking amount failed on settlement rejection: ${JSON.stringify(unblockAmount)}`,
        settlementLogger
      );
      await updateTransactionSettlementWithMessage(
        ourTx._id.toString(),
        false,
        txSettlement,
        ourTx.blockId,
        `Unblocking amount failed on settlement rejection: ${JSON.stringify(unblockAmount)}`,
        settlementLogger
      );

      return { success: false, error: 'Unblocking amount failed for settlement rejection' };
    } else {
      // 3.3 - If success, update transaction settlement with message and mark as resolved
      settlementLogger.info(
        `Unblocking amount successful from settlement rejection: ${JSON.stringify(unblockAmount)} for ${ourTx.retrievalReferenceNumber}`
      );
      await updateTransactionSettlementWithStatusAndResolventWithMessage(
        ourTx._id.toString(),
        true,
        TransactionStatusEnum.Declined,
        true,
        TransactionResolventTypeEnum.SettlementReject,
        dateISO,
        `Unblocking amount successful from settlement rejection: ${JSON.stringify(unblockAmount)}`,
        settlementLogger,
        txSettlement,
        ourTx.blockId
      );

      return { success: true };
    }
  } else {
    // 3.4 - Transactions with no block ID, mark as resolved and add message
    settlementLogger.info(
      `Transaction has no block ID: ${ourTx.retrievalReferenceNumber}, marking as resolved after settlement rejection`
    );
    await updateTransactionSettlementWithStatusAndResolventWithMessage(
      ourTx._id.toString(),
      true,
      TransactionStatusEnum.Declined,
      true,
      TransactionResolventTypeEnum.SettlementReject,
      dateISO,
      `Transaction has no block ID, marking as resolved after settlement rejection after settlement rejection`,
      settlementLogger,
      txSettlement
    );
    return { success: true };
  }
}

// ____________________ Helper Functions for Parsing ____________________

function parseSimpleDate(dateStr: string | number): Date | undefined {
  // Expects: "20230627" → YYYYMMDD
  if (!dateStr) return undefined;

  const str = String(dateStr);
  if (str.length !== 8) return undefined;

  const year = parseInt(str.substring(0, 4));
  const month = parseInt(str.substring(4, 6)) - 1;
  const day = parseInt(str.substring(6, 8));
  return new Date(year, month, day);
}

function cleanString(val: any, fallback = 'N/A'): string {
  if (val === null || val === undefined) return fallback;
  const str = String(val).trim();
  return str !== '' ? str : fallback;
}

function trimToLength(str: string, maxLength: number = 20): string {
  return str.length > maxLength ? str.substring(0, maxLength) : str;
}

function safeNum(val: any, fallback = 0): number {
  const n = Number(val);
  return isNaN(n) ? fallback : n;
}

/**
 * Parses a VISISO/VISA-style amount field into currency and decimal-adjusted amount.
 * Format: CCCDYAAAAAAAAAAAA
 * - CCC = 3-digit ISO 4217 numeric currency code
 * - D = decimal precision (0–9)
 * - A...A = amount (padded numeric string)
 */
export function parseVisaAmount(raw: string | number | null | undefined): { currency: string; amount: number } | null {
  const rawStr = String(raw ?? '').trim();

  if (!/^\d{16}$/.test(rawStr)) {
    return null; // <-- gracefully skip if not valid
  }

  const currencyCode = rawStr.slice(0, 3);
  const decimalDigit = parseInt(rawStr[3], 10);
  const amountStr = rawStr.slice(4).replace(/^0+/, '') || '0';

  const currency = currencyMap[currencyCode];
  if (!currency) {
    throw new Error(`Unknown currency code: ${currencyCode}`);
  }

  const padded = amountStr.padStart(decimalDigit + 1, '0');
  const amount =
    decimalDigit === 0
      ? parseInt(padded, 10)
      : parseFloat(padded.slice(0, -decimalDigit) + '.' + padded.slice(-decimalDigit));

  return { currency, amount };
}

/**
 * Validates the settlement transaction data.
 * @param txSettlement The settlement transaction data.
 * @param settlementLogger The logger instance.
 * @returns An object containing a success flag, error message, and the transaction data if successful.
 */
async function validateSettlementTransaction(
  txSettlement: any,
  settlementLogger: any
): Promise<{ success: boolean; error?: string; ourTx?: any }> {
  // 1 - Map XML fields to unique fields
  const uniqueFields = mapXmlToUniqueFields(txSettlement);
  if (typeof uniqueFields === 'string') {
    const msg = `Failed to map unique fields for settlement: ${uniqueFields}`;
    settlementLogger.error(msg);
    return { success: false, error: msg };
  }

  // 2 - Get transaction from our database by unique fields
  const txResult = await verifyUniqueTransaction(uniqueFields);
  // 2.1 - If there are multiple transactions, return error
  if (!txResult.isOne) {
    const msg = `Multiple transactions found for settlement with unique fields: ${JSON.stringify(uniqueFields)}`;
    settlementLogger.error(msg);
    return { success: false, error: msg };
  }

  // 2.2 - If there is no transaction, return error
  const ourTx = txResult.transaction;
  if (!ourTx) {
    const msg = `Transaction not found for settlement with unique fields: ${JSON.stringify(uniqueFields)}`;
    settlementLogger.error(msg);
    return { success: false, error: msg };
  }

  // 3 - Make sure we do have IBAN for tx
  if (!ourTx.accountIdentification1) {
    const msg = `Transaction missing IBAN: ${JSON.stringify(ourTx)}`;
    settlementLogger.error(msg);
    await addTransactionMessage(ourTx._id.toString(), msg, settlementLogger);
    return { success: false, error: msg };
  }

  // 4 - Check all important fields from settlement data if they match Tx data
  const comparisonResult = compareSettlementToTransaction(txSettlement, ourTx, compareSettlementMappings);
  const notEqualFields = Object.entries(comparisonResult)
    .filter(([, value]) => value.status === 'not_equal')
    .map(([key]) => key);
  if (notEqualFields.length > 0) {
    const msg = `Settlement data does not match transaction data for fields: ${notEqualFields.join(', ')}`;
    settlementLogger.error(msg);
    await addTransactionMessage(ourTx._id.toString(), msg, settlementLogger);
    return { success: false, error: msg };
  }

  // 4.1 - Save comparison result for missing fields
  await addTransactionMessage(
    ourTx._id.toString(),
    `Settlement data compared result: ${JSON.stringify(comparisonResult)}`,
    settlementLogger
  );

  return { success: true, ourTx };
}

// ____________________ Map Settlement Data For OraSys ____________________

// @Todo: Check if all fields are correct in OraSys
function mapToSettlementRecordCombined(
  settlementData: VisSettlementTransaction,
  tx: TransactionInterfaceData,
  cardNumber: string,
  cardHolder: string
): TransactionSettlementOrasysRecord {
  // Validate fields
  if (!tx.transactionCurrencyCode) {
    throw new Error('Failed to map settlement data: Missing required fields');
  } // Without block ID we do not allow credit Tx, since they should be already send
  const transAmountResult = parseVisaAmount(settlementData.VIS004_TRANS_AMT);
  const billingAmountResult = parseVisaAmount(settlementData.VIS006_CARDH_BILL_AMT);
  if (!transAmountResult || !billingAmountResult) {
    throw new Error('Failed to parse amount: Invalid format');
  }
  const { currency: transCodval, amount: transAmount } = transAmountResult;
  const { currency: billingCodval, amount: billingAmount } = billingAmountResult;
  const location1 = get2digitsLocationFromField43(settlementData.VIS043_CARD_ACCP_NAME_LOC);
  const location2 = get2digitsLocationFromField43(tx.cardAcceptorNameLocation);
  // @Notice: Visa docs handles date in GMT, but for now we use only UTC
  return {
    transDate: parseSimpleDate(settlementData.VIS012_TRANS_DATE),
    schDate: parseSimpleDate(settlementData.VIS015_STTL_DATE),
    valueDate: parseSimpleDate(settlementData.VIS015_STTL_DATE),
    transType: tx.transType.codeOraSys,
    descriptor: tx.fees?.taxNarrative || 'UNKNOWN DESCRIPTOR',
    mcc: tx.merchantCategoryCode || cleanString(settlementData.VIS026_MCC),
    region: location1 || location2 || 'N/A',
    authCode: cleanString(settlementData.VIS038_APPROVAL_CODE, tx.approvalCode),
    cardNo: cardNumber,
    accountNo: cleanString(tx.accountIdentification1),
    cardHolder: cardHolder,
    transAmount: transAmount,
    transCodval: transCodval,
    cardAmount: billingAmount,
    cardCodval: billingCodval,
    setlAmount: billingAmount,
    setlCodval: billingCodval,
    taxAmount: tx.fees?.taxAmount || 0,
    taxAmountFx: tx.fees?.taxAmountFx || 0,
    idBlock: tx.blockId,
    idRowSch: 0 // Not used by IL, set to 0
  };
}

export function mapToOraSysDataRecordFromCIP(
  tx: InsertTxCIPInterfaceFields,
  date: Date,
  blockId: number,
  approvalCode: string,
  cardNumber: string,
  cardHolder: string,
  IBAN: string,
  transactionType: TransactionTypeData
): TransactionSettlementOrasysRecord {
  // Validate fields
  if (!blockId) {
    throw new Error('Failed to map settlement data: Missing required fields');
  }

  return {
    transDate: date,
    schDate: date,
    valueDate: date,
    transType: transactionType.codeOraSys,
    descriptor: tx.feeNarrative,
    mcc: tx.mcc,
    region: tx.region,
    authCode: approvalCode,
    cardNo: cardNumber,
    accountNo: IBAN,
    cardHolder: cardHolder,
    transAmount: tx.transactionAmount,
    transCodval: tx.currency,
    cardAmount: tx.transactionAmount,
    cardCodval: tx.currency,
    setlAmount: tx.transactionAmount,
    setlCodval: tx.currency,
    taxAmount: tx.feeAmount,
    taxAmountFx: tx.FXFeeAmount,
    idBlock: blockId,
    idRowSch: 0 // Not used by IL, set to 0
  };
}

export function mapToOraSysTransactionRecordFromCIPApplyFee(
  mongoTx: TransactionInterfaceData,
  tx: InsertTxWithApplyingFeeCIPInterfaceFields,
  blockId: number,
  approvalCode: string,
  cardNumber: string,
  cardHolder: string,
  IBAN: string,
  transactionType: TransactionTypeData
): TransactionSettlementOrasysRecord {
  // Validate fields
  if (!blockId) {
    throw new Error('Failed to map settlement data: Missing required fields');
  }
  const date = new Date(); // Get the UTC current time

  return {
    transDate: date,
    schDate: date,
    valueDate: date,
    transType: transactionType.codeOraSys,
    descriptor: mongoTx.fees?.taxNarrative || 'UNKNOWN DESCRIPTOR',
    mcc: mongoTx.merchantCategoryCode || '4829', // 4829 is the default mcc for applying fees
    region: tx.region || tx.Location || 'N/A',
    authCode: approvalCode,
    cardNo: cardNumber,
    accountNo: IBAN,
    cardHolder: cardHolder,
    transAmount: tx.transactionAmount || 0,
    transCodval: 'EUR',
    cardAmount: tx.transactionAmount || 0,
    cardCodval: 'EUR',
    setlAmount: tx.transactionAmount || 0,
    setlCodval: 'EUR',
    taxAmount: mongoTx.fees.taxAmount || 0,
    taxAmountFx: mongoTx.fees.taxAmountFx || 0,
    idBlock: blockId,
    idRowSch: 0 // Not used by IL, set to 0
  };
}

/**
 * Maps XML data to unique fields combination with error handling and returns error string if fail
 */
export function mapXmlToUniqueFields(xml: any): TransactionUniqueCombinationFields | string {
  // Map and normalize fields (convert to string and trim spaces)
  const mapped: TransactionUniqueCombinationFields = {
    retrievalReferenceNumber: String(xml.VIS037_RRN || '').trim(),
    merchantCategoryCode: String(xml.VIS026_MCC || '').trim(),
    cardAcceptorTerminalId: String(xml.VIS041_CARD_ACCP_TERM_ID || '').trim(),
    cardAcceptorIdCode: String(xml.VIS042_CARD_ACCP_ID || '').trim()
  };

  // Find missing or empty fields (after trimming)
  const missingFields = Object.entries(mapped)
    .filter(([_, value]) => value === '') // now safe since all values are strings
    .map(([key]) => key);

  // If there are missing fields, return an error message string
  if (missingFields.length > 0) {
    return `Missing required XML fields for mapping: ${missingFields.join(', ')}`;
  }

  return mapped;
}
// ____________________ Settlement Comparison ____________________

type SettlementComparisonRule = {
  settlementKey: string;
  txKey: string;
  parseSettlement?: (val: any) => any;
  parseTx?: (val: any) => any;
  numericCompare?: boolean;
  allowTolerancePercent?: number;
  customCompare?: (sVal: string, tVal: string) => boolean;
};

type SettlementComparisonMappings = {
  [label: string]: SettlementComparisonRule;
};

/**
 * Compares a parsed settlement transaction (from XML/JSON) with a stored ISOTransactionModel document.
 * Returns a map of field names to comparison status.
 */
export function compareSettlementToTransaction(
  settlement: Record<string, any>,
  tx: Record<string, any>,
  mappings: SettlementComparisonMappings
): Record<
  string,
  {
    status: 'equal' | 'not_equal' | 'skipped_due_to_empty';
    settlementValue: string;
    txValue: string;
  }
> {
  const result: Record<string, any> = {};

  for (const label in mappings) {
    const { settlementKey, txKey, parseSettlement, parseTx, numericCompare, allowTolerancePercent, customCompare } =
      mappings[label];

    // raw values from settlement and tx
    const sRaw = settlement?.[settlementKey] ?? '';
    const tRaw = tx?.[txKey] ?? '';

    // optionally parse
    const sParsed = parseSettlement ? parseSettlement(sRaw) : sRaw;
    const tParsed = parseTx ? parseTx(tRaw) : tRaw;

    // normalize string output
    let sVal = normalizeFieldValue(sParsed);
    let tVal = normalizeFieldValue(tParsed);

    // optionally normalize numeric strings
    if (numericCompare) {
      sVal = normalizeNumberString(sVal);
      tVal = normalizeNumberString(tVal);
    }
    // decide status
    let status: 'equal' | 'not_equal' | 'skipped_due_to_empty';

    if (!sVal || !tVal) {
      status = 'skipped_due_to_empty';
    } else if (customCompare && customCompare(sVal, tVal)) {
      // Custom logic for name/location
      status = 'equal';
    } else if (numericCompare && allowTolerancePercent) {
      // Numeric comparison with tolerance
      const numS = Number(sVal);
      const numT = Number(tVal);
      if (!isNaN(numS) && !isNaN(numT)) {
        const diffPercent = (Math.abs(numS - numT) / numT) * 100;
        status = diffPercent <= allowTolerancePercent ? 'equal' : 'not_equal';
      } else {
        status = 'not_equal';
      }
    } else if (sVal === tVal) {
      status = 'equal';
    } else {
      status = 'not_equal';
    }

    result[label] = {
      status,
      settlementValue: sVal ?? '',
      txValue: tVal ?? ''
    };
  }

  return result;
}

/**
 * Mapping that points from TX value to Settlement value
 */
const compareSettlementMappings: {
  [key: string]: SettlementComparisonRule;
} = {
  primaryAccountNumber: { settlementKey: 'VIS002_PAN', txKey: 'primaryAccountNumber' },
  processingCode: { settlementKey: 'VIS003_PROC_CODE', txKey: 'processingCode' },
  transactionAmount: {
    settlementKey: 'VIS004_TRANS_AMT',
    txKey: 'transactionAmount',
    parseSettlement: (raw) => parseVisaAmount(raw)?.amount,
    numericCompare: true,
    allowTolerancePercent: 3 // Allow up to 3% difference
  },
  transactionCurrency: {
    settlementKey: 'VIS004_TRANS_AMT',
    txKey: 'transactionCurrencyCode',
    parseSettlement: (raw) => parseVisaAmount(raw)?.currency
  },
  cardholderBillingAmount: {
    settlementKey: 'VIS006_CARDH_BILL_AMT',
    txKey: 'cardholderBillingAmount',
    parseSettlement: (raw) => parseVisaAmount(raw)?.amount,
    numericCompare: true,
    allowTolerancePercent: 3 // Allow up to 3% difference
  },
  cardholderBillingCurrency: {
    settlementKey: 'VIS006_CARDH_BILL_AMT',
    txKey: 'cardholderBillingCurrencyCode',
    parseSettlement: (raw) => parseVisaAmount(raw)?.currency
  },
  merchantCategoryCode: { settlementKey: 'VIS026_MCC', txKey: 'merchantCategoryCode' },
  retrievalReferenceNumber: { settlementKey: 'VIS037_RRN', txKey: 'retrievalReferenceNumber' },
  approvalCode: { settlementKey: 'VIS038_APPROVAL_CODE', txKey: 'approvalCode' },
  cardAcceptorTerminalId: { settlementKey: 'VIS041_CARD_ACCP_TERM_ID', txKey: 'cardAcceptorTerminalId' },
  cardAcceptorIdCode: { settlementKey: 'VIS042_CARD_ACCP_ID', txKey: 'cardAcceptorIdCode' },
  cardAcceptorNameLocation: {
    settlementKey: 'VIS043_CARD_ACCP_NAME_LOC',
    txKey: 'cardAcceptorNameLocation',
    customCompare: (sVal, tVal) => {
      const normalize = (str: string) => str.replace(/[^A-Z]/gi, '').toUpperCase();
      const sNorm = normalize(sVal);
      const tNorm = normalize(tVal);
      return sNorm.includes(tNorm) || tNorm.includes(sNorm);
    }
  },
  IBAN: { settlementKey: 'VIS048_ADD_DATA_PRIV', txKey: 'accountIdentification1' }
};

// ____________________ Helper Functions ____________________

export function isRejectedFile(fileName: string): boolean {
  return fileName.toUpperCase().endsWith('_REJECTS.XML');
}

export function normalizeFieldValue(value: any): string {
  if (value === null || value === undefined || value === '') return '';

  if (typeof value === 'number') {
    return String(value).trim();
  }

  if (typeof value === 'string') {
    return value.trim();
  }

  return JSON.stringify(value).trim();
}

export function normalizeNumberString(value: string): string {
  return value.replace(/^0+/, '') || '0';
}
