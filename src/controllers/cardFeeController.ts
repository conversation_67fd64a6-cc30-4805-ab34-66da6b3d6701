import { Request, Response } from 'express';

import { addCardFee, addFXRate, createCardFeesInDatabase, getCardFees, getFXRates } from '../services/cardFeeService';
import { cardFeeLogger } from '../utils/logger';
import { processBulkData, sendBulkResponse, validateBulkRequest } from '../utils/bulkCardFeeHelpers';
import { fxRateSchema } from '../schemaValidations/FeeSchemaValidation';

const feeControllerLogger = cardFeeLogger.child({ controller: 'CardFeeController' });

/**
 * Searches for card fees based on query parameters
 * @param req - Express request object containing filter parameters in query
 * @param res - Express response object
 * @returns JSON response with array of matching card fees or error message
 */
export async function searchCardFees(req: Request, res: Response): Promise<void> {
  try {
    // Extract filter parameters from query
    const filter = req.query;
    const fees = await getCardFees(filter as any);

    res.status(200).json(fees);
  } catch (error) {
    feeControllerLogger.error({ error: 'Failed to search card fees', message: error });
    res.status(500).json({ error: 'Failed to search card fees', message: error });
  }
}

/**
 * Creates a new card fee
 * @param req - Express request object containing card fee data in body
 * @param res - Express response object
 * @returns JSON response with the newly created card fee or error message
 */
export async function createNewCardFee(req: Request, res: Response): Promise<void> {
  try {
    const { data: createdFee, error: errMsg } = await addCardFee(req.body);

    if (errMsg) {
      res.status(400).json({ errMsg });
      return;
    }

    res.status(201).json(createdFee);
  } catch (error) {
    feeControllerLogger.error({ error: 'Failed to create card fee', message: error });
    res.status(500).json({ error: 'Failed to create card fee', message: error });
  }
}

/**
 * Creates new card fees in bulk from file or array data.
 * The supported files are .xls, .xlsx, .csv, and any alternative CSV mime type.
 * @param req - Express request array containing card fees data in body
 * @param res - Express response object
 * @returns JSON response with the newly created card fees and an array of invalid records, if any. Otherwise an error message.
 * @example
 * // Example output:
 * {
 *   "created": 2, // number of created card fees
 *   "errors": 2, // number of errors
 *   "validRecords": [
 *     {
 *       "GLAccountName": "Card Issuing Fees",
 *       "feeCode": "01",
 *       ...
 *       "currency": "EUR",
 *       "fixedFee": 0.1,
 *       "percentageFee": 0,
 *       "supportsFXFee": false
 *     },
 *     {
 *       "GLAccountName": "Card Issuing Fees",
 *       "feeCode": "01",
 *       ...
 *       "currency": "EUR",
 *       "fixedFee": 0.11,
 *       "percentageFee": 0,
 *       "supportsFXFee": false
 *     }
 *   ],
 *   "errorDetails": [], // array of error messages from the addCardFee function called in the createCardFeesInDatabase
 *   "invalidRecords": [
 *     {
 *       "row": 8,
 *       "errors": [
 *         "\"displayDescriptor\" is required"
 *       ]
 *     },
 *     {
 *       "row": 53,
 *       "errors": [
 *         "\"feeCode\" is required"
 *       ]
 *     }
 *   ]
 * }
 */
export async function createBulkCardFees(req: Request, res: Response): Promise<void> {
  try {
    // Validate request format
    const validationResult = validateBulkRequest(req);
    if (!validationResult.isValid) {
      res.status(400).json({ error: validationResult.error });
      return;
    }

    // Process data based on source
    const processResult = await processBulkData(req);
    if (!processResult.success) {
      res.status(400).json(processResult.error);
      return;
    }

    // Create fees in database
    const creationResult = await createCardFeesInDatabase(processResult.data!.validRecords);

    // Send response
    sendBulkResponse(res, creationResult, processResult.data!.invalidRecords);
  } catch (error) {
    feeControllerLogger.error({ error: 'Failed to create card fees in bulk', message: error });
    res.status(500).json({ error: 'Failed to create card fees in bulk', message: error });
  }
}

// ______________________ FX RATES ______________________

/**
 * Searches for FX rates based on query parameters
 * @param req - Express request object containing filter parameters in query
 * @param res - Express response object
 * @returns JSON response with array of matching FX rates or error message
 */
export async function searchFXRates(req: Request, res: Response): Promise<void> {
  try {
    const filter = req.query;
    const rates = await getFXRates(filter as any);

    res.status(200).json(rates);
  } catch (error) {
    feeControllerLogger.error({ error: 'Failed to search FX rates', message: error });
    res.status(500).json({ error: 'Failed to search FX rates', message: error });
  }
}

/**
 * Creates a FX rate
 * @param req - Express request object containing FX rate data in body
 * @param res - Express response object
 * @returns JSON response with the newly created FX rate or error message
 */
export async function createNewFXRate(req: Request, res: Response): Promise<void> {
  try {
    const { data: createdRate, error: errMsg } = await addFXRate(req.body);

    if (errMsg) {
      res.status(400).json({ errMsg });
      return;
    }

    res.status(201).json(createdRate);
  } catch (error) {
    feeControllerLogger.error({ error: 'FX Rate creation failed', message: error });
    res.status(500).json({ error: 'FX Rate creation failed', message: error });
  }
}

/**
 * Creates a new FX rates in bulk
 * @param req - Express request array containing FX rates data in body
 * @param res - Express response object
 * @returns JSON response with the newly created FX rates or error message
 */
export async function createBulkFXRates(req: Request, res: Response): Promise<void> {
  try {
    const arrayOfRates = Array.isArray(req.body) ? req.body : [];
    const arrayOfCreatedRates: any[] = [];
    const arrayOfErrors: any[] = [];

    for (const rate of arrayOfRates) {
      try {
        const validationSchema = fxRateSchema.validate(rate);
        if (validationSchema.error) {
          arrayOfErrors.push(validationSchema.error.message);
          continue;
        }

        const { data: createdRate, error: errMsg } = await addFXRate(rate);

        if (errMsg) {
          arrayOfErrors.push(errMsg);
        } else {
          arrayOfCreatedRates.push(createdRate);
        }
      } catch (error) {
        arrayOfErrors.push(error);
      }
    }

    feeControllerLogger.info(`Created ${arrayOfCreatedRates.length} fx rates for bulk creation`);
    if (arrayOfErrors.length > 0) {
      feeControllerLogger.error(`Failed to create ${arrayOfErrors.length} fx rates for bulk creation`);
      feeControllerLogger.error({ errors: arrayOfErrors });
    }

    if (arrayOfCreatedRates.length !== 0 && arrayOfErrors.length === 0) {
      res.status(201).json(arrayOfCreatedRates);
    } else if (arrayOfCreatedRates.length === 0 && arrayOfErrors.length !== 0) {
      res.status(400).json(arrayOfErrors);
    } else {
      res.status(206).json(arrayOfCreatedRates.concat(arrayOfErrors));
    }
  } catch (error) {
    feeControllerLogger.error({ error: 'Failed to create fx rate', message: error });
    res.status(500).json({ error: 'Failed to create fx rates', message: error });
  }
}
