import { Request, Response, NextFunction } from 'express';
import { XMLParser } from 'fast-xml-parser';
import fs from 'fs/promises';
import { settlementLogger } from '../utils/logger';
import { handleSettlementFile, isRejectedFile } from '../services/settlementService';

export async function ParseAndSettleXmlFile(req: Request, res: Response, next: NextFunction): Promise<void> {
  const files = req.files as Express.Multer.File[];

  if (!files || files.length !== 1) {
    res.status(400).json({ error: 'No file uploaded or too many files' });
    return;
  }

  const file = files[0]; // Grab the first uploaded file (should be one)
  const filePath = file.path;

  try {
    // Step 1: Read the file
    const xmlContent = await fs.readFile(filePath, 'utf-8');

    // Step 2: Parse safely
    const parser = new XMLParser({
      ignoreAttributes: false,
      processEntities: false // disable entity expansion
    });
    const parsedJson = parser.parse(xmlContent);

    // Step 3: Handle Settlement File
    const isRejected = isRejectedFile(file.originalname);
    const data = await handleSettlementFile(parsedJson, isRejected);
    settlementLogger.info(`Result from settlement:\n${JSON.stringify(data, null, 2)}`);

    // Step 4: Respond with parsed JSON
    res.json(parsedJson);
  } catch (error) {
    settlementLogger.error('Parsing Error:', error);
    res.status(500).json({ error: 'Failed to parse XML file' });
  } finally {
    // Step 5: Always delete file afterward
    try {
      await fs.unlink(filePath);
      settlementLogger.info(`Temp file deleted: ${filePath}`);
    } catch (unlinkError) {
      settlementLogger.warn('Failed to delete temp file:', unlinkError);
    }
    next();
  }
}
