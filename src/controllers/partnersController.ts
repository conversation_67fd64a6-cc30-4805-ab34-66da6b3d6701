import { Request, Response } from 'express';
import { createPartner, findPartnerByUrl } from '../queries/MongoPartners';
import { partnerLogger } from '../utils/logger';

// _______ Create _______ Partner _______ Account _______

/**
 * Creates a new partner with name, public key and optional webhook applicant url
 * @param req The request object containing the body params
 * @param res The response object
 */
export async function CreatePartner(req: Request, res: Response): Promise<void> {
  try {
    const { url, name, publicKey, webhookApplicantUrl } = req.body;

    if (!url || !name || !publicKey) {
      res.status(400).json({
        error: 'Missing required fields',
        required: ['url', 'name', 'publicKey']
      });
      return;
    }

    const findByUrl = await findPartnerByUrl(url);
    if (findByUrl) {
      res.status(400).json({ error: 'Partner with this URL already exists' });
      return;
    }

    const partner = await createPartner({
      url,
      name,
      publicKey,
      webhookApplicantUrl
    });

    partnerLogger.info(partner);
    res.status(201).json(partner);
  } catch (error) {
    partnerLogger.error({ error: 'Failed to create partner', message: error });
    res.status(500).json({ error: 'Failed to create partner' });
  }
}
