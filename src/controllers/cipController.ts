import { getCurrentUTCDateISO, convertDateToISO_UTC, makeBackendRequest } from '../utils/helpers';
import { insertOraSysTransaction } from '../queries/SqlQueries';
import { Request, Response } from 'express';
import { CIPConfig } from '../config';
import { createClientAndDebitAccountSchema } from '../schemaValidations/ClientSchemaValidation';
import { createClient } from '../queries/MongoClients';
import { createDebitAccount } from '../queries/MongoDebit';
import { CIPLogger } from '../utils/logger';
import { Iso8583Transaction } from '../models/transactionISOSchema';
import { TransactionQueryParams, TransactionResponse } from '../interfaces/transactionInterface';
import {
  convertCIPFieldsToNamedInterface,
  convertCIPFieldsToNamedInterfaceApplyFee,
  getCardAndAccountOrFail,
  handleOraSysInsertFailure,
  isClientOrAccountRegistered,
  mapToSimpleTransactions
} from '../services/cipService';
import { checkAvailableBalanceByIBAN } from '../services/userService';
import {
  getTransactionByRetrievalReferenceNumber,
  createTransaction,
  addTransactionMessage,
  updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved
} from '../queries/MongoTransactions';
import {
  InsertTxCIPInterfaceFields,
  TransactionResolventTypeEnum,
  TransactionSourceEnum,
  TransactionStatusEnum
} from '../interfaces/transactionInterface';
import {
  attemptBlockAmountWithRetries,
  calculateFeeAmount,
  checkIBANandCardID,
  denyTransactionWithMessage,
  generateValidAuthCode,
  mapProcessingCodeToOraSysDetails,
  validateBalance
} from '../services/transactionService';
import {
  mapToOraSysDataRecordFromCIP,
  mapToOraSysTransactionRecordFromCIPApplyFee
} from '../services/settlementService';
import { registerCardSchema } from '../schemaValidations/CardCreatedValidation';
import {
  applyFeeOnTxSchema,
  checkFeeForTxSchema,
  transactionWithFeeCIPSchema
} from '../schemaValidations/FeeTransactionValidation';
import { allOraSysCodes, findTransactionTypeByOraSysCode } from '../constants/transactionType';
import { findCardFeeByUniqueFields } from '../queries/MongoFees';
import { createCard, findCardByCardKey } from '../queries/MongoCards';

/**
 * Register a client and then creates a debit account for it
 * @param req The request object containing the query parameters
 * @param res The request object containing the query parameters
 * @returns Registered client and created debit account!
 */
export async function handleCIPClient(req: Request, res: Response) {
  const { error } = createClientAndDebitAccountSchema.validate(req.body, { abortEarly: false });

  if (error) {
    const errorMessages = error.details.map((err) => err.message.replace(/"/g, ''));
    return res.status(400).json({ messages: errorMessages });
  }

  // Validate client ID or IBAN does not repeat
  const clientCode = req.body.client.clientCode;
  const iban = req.body.debit.accountNumber;

  const clientFound = await isClientOrAccountRegistered(clientCode, iban);
  if (clientFound) {
    res.status(400).json({
      error: 'Client ID or IBAN already exists',
      message: 'Client ID or IBAN already exists'
    });
    return;
  }

  // Create client
  const status = await registerClient(req, res);
  if (status !== 'OK') {
    return;
  }
  await registerDebitAccount(req, res);
  return;
}

// ______ ----- Register ----- ______ ----- Client ----- ______

/**
 * Registers a client in CIP and saves it into Mongo
 * @param req The request object containing the body data of the debit account and client
 * @param res The response object
 * @returns OK if everything went well
 */
async function registerClient(req: Request, res: Response) {
  const token = req.headers.authorization?.split(' ')[1];

  try {
    // Send request for login to CIP:
    const response = await makeBackendRequest({
      method: 'POST',
      maxBodyLength: Infinity,
      url: CIPConfig.ryvylCIPUrl + '/api/il/clients',
      data: req.body.client,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: 'Bearer ' + token
      }
    });

    if (response.status !== 200 && response.status !== 201) {
      CIPLogger.error(`Error creating client: ${JSON.stringify(response.data)}`);
      res.status(400).send(response.data);
      return;
    }

    // Create client
    const client = await createClient(response.data.data);
    if (!client) {
      res.status(400).send('client not created');
      return;
    }

    CIPLogger.info(`Client created: ${JSON.stringify(client)}`);
    return 'OK';
  } catch (error) {
    CIPLogger.error(error);
    res.status(500).send(error);
    return;
  }
}

// ____ --- Create --- ____ --- Debit --- ____ --- Account --- ____

/**
 * Registers a debit account & 1st debit card in CIP and saves it into Mongo
 * @param req The request object containing the body data of the debit account and client
 * @param res The response object
 * @returns Response message that everything went well
 */
async function registerDebitAccount(req: Request, res: Response) {
  const body = req.body;
  body.debit.accountHolder = `${body.client.personalInfo.firstName} ${body.client.personalInfo.lastName}`;
  body.debit.clientCode = body.client.clientCode;

  const token = req.headers.authorization?.split(' ')[1];

  try {
    // Send request for login to CIP:
    const response = await makeBackendRequest({
      method: 'POST',
      maxBodyLength: Infinity,
      url: CIPConfig.ryvylCIPUrl + '/api/il/debit-account',
      data: body.debit,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + token
      }
    });

    if (response.status !== 200 && response.status !== 201) {
      CIPLogger.error(`Error creating debit account for client ${body.clientCode}: ${JSON.stringify(response.data)}`);
      res.status(400).send(response.data);
      return;
    }

    // Add the customer type since CIP does not return it!
    response.data.account.customerType = body.debit.customerType;

    // Create debit account
    const debitAcc = await createDebitAccount(response.data.account);

    // Find card by cardKey if we already saved it from the webhook!
    let debitCard;
    const foundCard = await findCardByCardKey(response.data.card.cardKey);
    if (foundCard) {
      debitCard = foundCard;
      CIPLogger.warn(`Card already saved from webhook: ${JSON.stringify(foundCard.cardKey)}`);
    } else {
      debitCard = await createCard(response.data.card);
    }

    if (!debitAcc || !debitCard) {
      CIPLogger.error(`Error creating debit account or for client in mongo for: ${response.data}`);
      res.status(400).send('Debit account or card not created in database');
      return;
    }

    CIPLogger.info(`Debit account created: ${JSON.stringify(debitAcc)}, with Card: ${JSON.stringify(debitCard)}`);
    res.status(201).send('Client registered successfully and debit account was created!');
  } catch (error) {
    CIPLogger.error(error);
    res.status(500).send(error);
  }
}

// _______ Get _______ Transactions _______ For _______ Partner _______

/**
 * Returns all transactions for a partner based on query params
 * @returns The transaction in simplified version
 */
export const GetTransactions = async (req: Request, res: Response) => {
  try {
    const {
      accountIdentification,
      cardId,
      dateStart,
      dateEnd,
      page = '1',
      limit = '10',
      status,
      type: txType
    } = req.query as TransactionQueryParams;

    // Handle date parsing
    let startDate: Date;
    let endDate: Date;

    // 1 - If date is not provided set to the last 24 hours
    if (!dateStart && !dateEnd) {
      endDate = new Date();
      startDate = new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
    } else if (!dateStart || !dateEnd) {
      res.status(400).json({
        error: 'Missing required parameter',
        message: 'dateStart and dateEnd must be provided together'
      });
      return;
    } else {
      startDate = new Date(dateStart);
      endDate = new Date(dateEnd);
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        res.status(400).json({
          error: 'Invalid date format',
          message: 'dateStart and dateEnd must be in ISO 8601 format'
        });
        return;
      }
    }

    // 2 - Parse pagination
    const pageNum = Number(page);
    const limitNum = Number(limit);
    if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
      res.status(400).json({
        error: 'Invalid parameter values',
        message: 'page and limit must be positive numbers'
      });
      return;
    }

    // 3 - Start building query
    const query: any = {
      createdAt: {
        $gte: startDate.toISOString(),
        $lte: endDate.toISOString()
      }
    };

    if (accountIdentification) {
      query.accountIdentification1 = accountIdentification;
    }

    if (cardId) {
      query.cardId = cardId;
    }

    if (status) {
      const allowedStatuses = Object.values(TransactionStatusEnum);
      if (!allowedStatuses.includes(status)) {
        res.status(400).json({
          error: 'Invalid status value',
          message: `Status must be one of ${allowedStatuses.join(', ')}`
        });
        return;
      }
      query.transStatus = status;
    }

    if (txType) {
      if (!allOraSysCodes.includes(txType)) {
        res.status(400).json({
          error: 'Invalid transaction type value',
          message: `Transaction type must be one of ${allOraSysCodes.join(', ')}`
        });
        return;
      }
      query.$and = [{ 'transType.codeOraSys': txType }, { transType: { $type: 'object' } }];
    }

    // 4 - Get total count and build the correct message
    const total = await Iso8583Transaction.countDocuments(query);
    if (total === 0) {
      let message = `No transactions found`;
      if (accountIdentification) message += ` for IBAN ${accountIdentification}`;
      if (cardId) message += ` with card ID ${cardId}`;
      if (status) message += ` with status ${status}`;
      if (txType) message += ` with transaction type code ${txType}`;
      message += ` in the specified date range (${startDate.toISOString()} - ${endDate.toISOString()})`;

      res.status(404).json({
        error: 'No transactions found',
        message
      });
      return;
    }

    // 5 - Get transactions
    const skip = (pageNum - 1) * limitNum;
    const transactions = await Iso8583Transaction.find(query).sort({ transactionDate: -1 }).skip(skip).limit(limitNum);

    // 6 - Map transactions to simple transactions and return response
    const simpleTransactions = mapToSimpleTransactions(transactions);
    const response: TransactionResponse = {
      transactions: simpleTransactions,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        totalPages: Math.ceil(total / limitNum)
      }
    };

    res.status(200).json(response);
  } catch (error) {
    CIPLogger.error('Error fetching transactions:', error);
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};

// _______ Get _______ Available _______ Balance _______ For _______ IBAN _______

/**
 * Returns the available balance for a given IBAN
 * @returns The available balance
 */
export const GetAvailableBalance = async (req: Request, res: Response) => {
  try {
    const { accountIdentification } = req.query;

    if (!accountIdentification) {
      res.status(400).json({
        error: 'Missing required parameter',
        message: 'accountIdentification is required'
      });
      return;
    }

    const balance = await checkAvailableBalanceByIBAN(String(accountIdentification));
    if (!balance) {
      res.status(404).json({
        error: 'No balance found',
        message: `No balance found for IBAN ${accountIdentification}`
      });
      return;
    }

    res.status(200).json({ availableBalance: balance });
  } catch (error) {
    CIPLogger.error('Error fetching available balance:', error);
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};

// _______ Insert _______ New _______ Card _______ From _______ Partner _______

/**
 * Webhook for registering a new card in CIP and saves it into Mongo
 * @returns Response message that everything went well
 */
export const RegisterCard = async (req: Request, res: Response) => {
  try {
    const { event, cardData } = req.body;

    const foundCard = await findCardByCardKey(cardData.cardKey);
    if (foundCard) {
      res.status(400).json({
        error: 'Card key already exists',
        message: 'Card key already exists'
      });
      return;
    }

    if (event !== 'card_created') {
      res.status(400).json({
        error: 'Invalid payload format',
        message: 'Only card_created events are supported'
      });
      return;
    }

    const validation = registerCardSchema.validate(req.body);
    if (validation.error) {
      res.status(400).json({
        error: 'Invalid payload format',
        message: validation.error.message
      });
      return;
    }

    const card = await createCard(cardData);
    if (!card) {
      res.status(400).json({
        error: 'Failed to create card',
        message: 'Failed to create card'
      });
      return;
    }

    CIPLogger.info(`Card created: ${JSON.stringify(card)}`);
    res.status(201).json({
      message: 'Webhook received successfully'
    });
  } catch (error) {
    CIPLogger.error(`Error registering card: ${JSON.stringify(req.body)}`, error);
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};

// _______ Insert _______ Transaction _______ For _______ Partner _______

/**
 * Creates a new transaction blocks needed amount and sends to OraSys with a given fee
 * @returns The created transaction and message
 */
export const InsertTransactionFee = async (req: Request, res: Response) => {
  let tx: any;

  try {
    const transaction: InsertTxCIPInterfaceFields = req.body;

    // 1 -  Validators of body and if tx data is valid
    const validation = transactionWithFeeCIPSchema.validate(req.body);
    if (validation.error) {
      res.status(400).json({
        error: 'Invalid payload format',
        message: validation.error.message
      });
      return;
    }

    // 1.1 - Check if tx already exists
    const existingTx = await getTransactionByRetrievalReferenceNumber(transaction.retrievalReferenceNumber);
    if (existingTx) {
      res.status(400).json({
        error: 'Transaction with that retrievalReferenceNumber already exists',
        message: 'Failed to create transaction'
      });
      return;
    }

    // 1.2 - Check if currency is EUR
    if (transaction.currency !== 'EUR') {
      res.status(400).json({
        error: 'Not supporting other currencies than EUR',
        message: 'Failed to create transaction'
      });
      return;
    }

    // 1.3 - Check if IBAN and cardID are valid and match
    const { message: errorMsg, IBAN } = await checkIBANandCardID(transaction.IBAN, transaction.cardID);
    if (!IBAN) {
      CIPLogger.error(`${errorMsg} for ${JSON.stringify(transaction)}`);
      res.status(400).json({
        error: errorMsg,
        message: errorMsg
      });
      return;
    } else if (errorMsg) {
      CIPLogger.warn(`${errorMsg} for ${JSON.stringify(transaction)}`);
    }

    // 2 - Generate auth code and check if it is valid and not
    const date = new Date();
    const dateUTC = convertDateToISO_UTC(date);
    const authCode = await generateValidAuthCode(transaction.retrievalReferenceNumber, IBAN, dateUTC);
    if (!authCode) {
      CIPLogger.error(`Error generating auth code: ${JSON.stringify(transaction)}`);
      res.status(400).send(`Error generating auth code: ${JSON.stringify(transaction)}`);
      return;
    }

    // 3 - Get the transaction type
    // @Notice: Manage the transaction type properly, for now we handle CIP only for POS1 payments!
    // Currently we do not use the fee type, only save it into the database and use the type POS1
    const transactionType = mapProcessingCodeToOraSysDetails('000000'); // POS1 - Goods and Services
    if (!transactionType) {
      CIPLogger.error(`Transaction type not mapped: ${JSON.stringify(transaction)}`);
      res.status(400).send(`Transaction type not mapped: ${JSON.stringify(transaction)}`);
      return;
    }

    // 4 - Create TX in Mongo
    const formattedTx = convertCIPFieldsToNamedInterface(transaction);
    tx = await createTransaction(
      formattedTx,
      TransactionSourceEnum.CIP,
      transaction,
      {
        taxType: transaction.feeType,
        taxAmount: transaction.feeAmount,
        taxAmountFx: transaction.FXFeeAmount,
        taxNarrative: transaction.feeNarrative
      },
      transactionType,
      CIPLogger
    );
    if (!tx) {
      res.status(400).json({
        error: 'Failed to create transaction',
        message: 'Failed to create transaction'
      });
      return;
    }

    CIPLogger.info(`CIP Transaction Created: ${JSON.stringify(transaction)}`);

    // Expected to be in EURO from 1.2 check
    const amount: number = (tx.transactionAmount + tx.fees.taxAmount + tx.fees.taxAmountFx).toFixed(2);
    // 5 - If amount or fee is not 0, check if there are sufficient funds and block
    if (amount > 0) {
      // 5.1 - Validate balance
      const balanceResult = await validateBalance(IBAN, amount, tx._id.toString(), transaction, CIPLogger);
      if (!balanceResult.success) {
        await denyTransactionWithMessage(tx._id.toString(), balanceResult.message, CIPLogger);
        res.status(balanceResult.statusCode).send(balanceResult.message);
        return;
      }

      // 5.2 - Get card and account
      const cardResult = await getCardAndAccountOrFail({
        txId: tx._id.toString(),
        txData: transaction,
        CIPLogger
      });
      if (!cardResult.success) {
        res.status(400).send(cardResult.error);
        return;
      }
      const { card, cardAccount } = cardResult.data;

      // 5.3 - Block amount
      const blockResult = await attemptBlockAmountWithRetries({
        IBAN,
        amount,
        currency: 'EUR',
        txId: tx._id.toString(),
        logger: CIPLogger,
        bodyOrTx: transaction,
        authCode
      });
      if (!blockResult.success || !blockResult.blockId) {
        res
          .status(400)
          .send(`Error occurred while blocking amount: ${JSON.stringify(transaction)}, Block result: ${blockResult}`);
        return;
      }

      // 5.4 - Prepare OraSys query
      const oraSysData = mapToOraSysDataRecordFromCIP(
        transaction,
        date,
        blockResult.blockId,
        authCode,
        card?.cardMask || 'N/A',
        cardAccount.accountHolder,
        IBAN,
        transactionType
      );

      // 5.5 - Insert OraSys data and check if it was successful
      const oraSysResult = await insertOraSysTransaction(oraSysData);
      if (!oraSysResult.success) {
        // 5.5.1 - Handle OraSys insert failure
        await handleOraSysInsertFailure({
          txId: tx._id.toString(),
          blockId: blockResult.blockId,
          oraSysData,
          oraSysResult,
          txInfo: transaction,
          response: res,
          CIPLogger
        });
        return;
      }

      // 6 - Update transaction resolvent with message on success
      const returnMessage = `CIP Transaction send to OraSys successfully: ${JSON.stringify(transaction)}, blocked amount: ${amount}`;
      await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
        tx._id.toString(),
        true,
        true,
        dateUTC,
        TransactionResolventTypeEnum.DirectTransaction,
        returnMessage,
        'CIP Transaction send to OraSys successfully',
        TransactionStatusEnum.Completed,
        CIPLogger,
        oraSysData
      );

      CIPLogger.info(returnMessage);
      res.status(200).json(returnMessage);
      return;
    }

    // 7 - If amount and fee is 0, resolve transaction
    const returnMessage = `Transaction saved, with 0 amount and fee: ${JSON.stringify(transaction)}`;
    await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
      tx._id.toString(),
      true,
      true,
      dateUTC,
      TransactionResolventTypeEnum.ZeroAmount,
      returnMessage,
      'Transaction saved, with 0 amount and fee',
      TransactionStatusEnum.Completed,
      CIPLogger
    );
    CIPLogger.info(returnMessage);
    res.status(200).json(returnMessage);
  } catch (error) {
    CIPLogger.error('Error inserting transaction:', error);
    // 8 - If error, add message to transaction
    if (tx) {
      await addTransactionMessage(tx._id.toString(), `Unexpected Error: ${error}`, CIPLogger);
    }
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};

// _______ Check _______ Fee _______ For _______ Tx _______

/**
 * Returns the fee for a given transaction
 * @returns The fee
 */
export const GetTransactionFee = async (req: Request, res: Response) => {
  try {
    const txData = req.body;

    // 1 -  Validators of body and if tx data is valid
    const validation = checkFeeForTxSchema.validate(txData);
    if (validation.error) {
      res.status(400).json({
        error: 'Invalid payload format',
        message: validation.error.message
      });
      return;
    }

    // 2 - Find the card fee
    const fee = await findCardFeeByUniqueFields(
      txData.OperationType,
      txData.CustomerType,
      txData.ProductType,
      txData.Location,
      txData.Modifier
    );
    if (!fee) {
      res.status(400).json({
        error: 'Fee not found',
        message: 'Fee not found'
      });
      return;
    }

    // 3 - Return the fee
    res.status(200).json(fee);
  } catch (error) {
    CIPLogger.error('Error checking fee for transaction:', error);
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};

// _______ Insert _______ Transaction _______ With _______ Applying _______ Fee _______

/**
 * Creates a new transaction blocks needed amount and sends to OraSys with the fee applied
 * @returns The created transaction with message
 */
export const InsertTxWithApplyingFee = async (req: Request, res: Response) => {
  let tx: any;

  try {
    const txData = req.body;

    // 1 -  Validators of body and if tx data is valid
    const validation = applyFeeOnTxSchema.validate(txData);
    if (validation.error) {
      res.status(400).json({
        error: 'Invalid payload format',
        message: validation.error.message
      });
      return;
    }

    // 1.1 - Check if tx already exists
    const existingTx = await getTransactionByRetrievalReferenceNumber(txData.retrievalReferenceNumber);
    if (existingTx) {
      res.status(400).json({
        error: 'Transaction with that retrievalReferenceNumber already exists',
        message: 'Failed to create transaction'
      });
      return;
    }

    // 2 - Find and calculate the fee
    const fee = await findCardFeeByUniqueFields(
      txData.OperationType,
      txData.CustomerType,
      txData.ProductType,
      txData.Location,
      txData.Modifier
    );
    if (!fee) {
      CIPLogger.error(
        `Fee not found for operation type: ${txData.operationType}, customer type: ${txData.customerType}, product type: ${txData.productType}, location: ${txData.Location}, modifier: ${txData.Modifier}`
      );
      res.status(400).json({
        error: 'Fee not found',
        message: `Fee not found for operation type: ${txData.operationType}, customer type: ${txData.customerType}, product type: ${txData.productType}, location: ${txData.Location}, modifier: ${txData.Modifier}`
      });
      return;
    }
    const amountTx: number = Number(txData.transactionAmount) || 0; // Expected always in EURO
    const feeAmount = calculateFeeAmount(amountTx, fee);

    // 2.1 - Get the transaction type based on the feeCode
    const txTypeData = findTransactionTypeByOraSysCode(fee.feeCode);
    if (!txTypeData) {
      CIPLogger.error(`Transaction type not mapped: ${JSON.stringify(txData)}`);
      res.status(400).send(`Transaction type not mapped: ${JSON.stringify(txData)}`);
      return;
    }

    // 3 - Create TX in Mongo
    const formattedTx = convertCIPFieldsToNamedInterfaceApplyFee(txData);
    tx = await createTransaction(
      formattedTx,
      TransactionSourceEnum.CIP,
      txData,
      {
        taxType: fee.operationType,
        taxAmount: feeAmount,
        taxAmountFx: 0,
        taxNarrative: fee.displayDescriptor
      },
      txTypeData,
      CIPLogger
    );
    if (!tx) {
      res.status(400).json({
        error: 'Failed to create transaction',
        message: 'Failed to create transaction'
      });
      return;
    }

    CIPLogger.info(`CIP Apply Fee Transaction Created: ${JSON.stringify(txData)}`);

    // 4 - Amount check
    const dateUTC = getCurrentUTCDateISO();
    const totalAmount: number = (tx.fees.taxAmount + tx.transactionAmount).toFixed(2);
    if (totalAmount > 0) {
      // 4.1 - If amount is more than 0, check if there are sufficient funds
      const balanceResult = await validateBalance(txData.IBAN, totalAmount, tx._id.toString(), txData, CIPLogger);
      if (!balanceResult.success) {
        await denyTransactionWithMessage(tx._id.toString(), balanceResult.message, CIPLogger);
        res.status(balanceResult.statusCode).send(balanceResult.message);
        return;
      }

      // 5 - Generate Auth Code:
      const authCode = await generateValidAuthCode(txData.retrievalReferenceNumber, txData.IBAN, dateUTC);
      if (!authCode) {
        const errMsg = `Error generating auth code: ${JSON.stringify(txData)}`;
        CIPLogger.error(errMsg);
        await denyTransactionWithMessage(tx._id.toString(), errMsg, CIPLogger);
        res.status(400).send(errMsg);
        return;
      }

      // 6 - Get card details for account
      const cardResult = await getCardAndAccountOrFail({
        txId: tx._id.toString(),
        txData,
        CIPLogger
      });
      if (!cardResult.success) {
        res.status(400).send(cardResult.error);
        return;
      }
      const { card, cardAccount } = cardResult.data;

      // 7 - Block Amount
      const blockResult = await attemptBlockAmountWithRetries({
        IBAN: txData.IBAN,
        amount: totalAmount,
        currency: 'EUR',
        txId: tx._id.toString(),
        logger: CIPLogger,
        bodyOrTx: txData,
        authCode: authCode
      });
      if (!blockResult.success || !blockResult.blockId) {
        res.status(400).send(`Error occurred while blocking amount: ${JSON.stringify(txData)}`);
        return;
      }

      // 8 - Format data for OraSys
      const dataOraSys = mapToOraSysTransactionRecordFromCIPApplyFee(
        tx,
        txData,
        blockResult.blockId,
        authCode,
        card?.cardMask || 'N/A',
        cardAccount.accountHolder,
        txData.IBAN,
        txTypeData
      );

      // 9 - Insert OraSys data and check if it was successful
      const oraSysResult = await insertOraSysTransaction(dataOraSys);
      if (!oraSysResult.success) {
        // 9.1 - Handle OraSys insert failure
        await handleOraSysInsertFailure({
          txId: tx._id.toString(),
          blockId: blockResult.blockId,
          oraSysData: dataOraSys,
          oraSysResult,
          txInfo: txData,
          response: res,
          CIPLogger
        });
        return;
      }

      // 10 - Update transaction resolvent with message on success
      const returnMessage = `CIP Applied Fee Transaction send to OraSys successfully: ${JSON.stringify(txData)}, blocked amount: ${totalAmount}`;
      await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
        tx._id.toString(),
        true,
        true,
        dateUTC,
        TransactionResolventTypeEnum.FeeTransaction,
        returnMessage,
        'CIP Applied Fee Transaction send to OraSys successfully',
        TransactionStatusEnum.Completed,
        CIPLogger,
        dataOraSys
      );

      CIPLogger.info(returnMessage);
      res.status(201).json(returnMessage);
      return;
    }

    // 11 - If amount and fee is 0, resolve transaction
    const returnMessage = `Transaction saved, with 0 amount and fee: ${JSON.stringify(txData)}`;
    await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
      tx._id.toString(),
      true,
      true,
      dateUTC,
      TransactionResolventTypeEnum.ZeroAmount,
      returnMessage,
      'Transaction saved, with 0 amount and fee',
      TransactionStatusEnum.Completed,
      CIPLogger
    );
    CIPLogger.info(returnMessage);
    res.status(200).json(returnMessage);
  } catch (error) {
    CIPLogger.error('Error applying and creating transaction fee:', error);
    // 12 - If error, add message to transaction
    if (tx) {
      await addTransactionMessage(tx._id.toString(), `Unexpected Error: ${error}`, CIPLogger);
    }
    res.status(500).json({ message: 'Something went wrong, please try again!' });
  }
};
