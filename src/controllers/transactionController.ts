import { Request, Response } from 'express';

import { heartbeatLogger, requestLogger, adviceLogger, reversalLogger, transactionLogger } from '../utils/logger';
import { checkBalanceInfoByIBAN } from '../services/userService';
import {
  generateField54,
  generateValidAuthCode,
  mapFieldsFromTxToBlockID,
  isSameCurrencyAndSufficient,
  processVisaAmountsWithValidation,
  convertStringFieldsToNamedInterface,
  attemptBlockAmountWithRetries,
  mapToOraSysDataRecordFromCreditTx,
  checkIBANandCardID,
  buildApprovedResponseFieldsFinancial,
  mapProcessingCodeToOraSysDetails,
  ApplyFXFee,
  ApplyNormalFee,
  denyTransactionWithMessageAndResponse,
  get2digitsLocationFromField43
} from '../services/transactionService';
import {
  validateExistingTransaction,
  validateReversalDetails,
  validateTransaction
} from '../services/transactionValidationService';
import { unblockAmountBy<PERSON>lockID } from '../queries/SqlProcedures';
import {
  createTransaction,
  updateTransactionApprovalCode,
  addTransactionMessage,
  updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved,
  updateTransactionProcessingOutcomeWithMessageAndStatus,
  updateTransactionProcessingOutcomeResponse,
  updateTransactionReversalWithMessage,
  updateTransactionReversalWithStatusAndResolventWithMessage
} from '../queries/MongoTransactions';
import { getCardAndAccount } from '../services/cipService';
import { insertOraSysTransaction } from '../queries/SqlQueries';
import {
  TransactionResolventTypeEnum,
  TransactionSourceEnum,
  TransactionStatusEnum
} from '../interfaces/transactionInterface';
import { sendEmailForUnexpectedError } from '../services/cronService';
import { CustomerType, OperationType, ProductType } from '../constants/fees';

const transactionHandlers: { [key: number]: (req: Request, res: Response) => void } = {
  1804: HeartBeat,
  1814: HeartBeat,
  1100: RequestTransaction,
  1101: RequestTransaction,
  1200: RequestTransaction,
  1201: RequestTransaction,
  1120: AdviceTransaction,
  1121: AdviceTransaction,
  1220: AdviceTransaction,
  1221: AdviceTransaction,
  1420: ReversalTransaction,
  1421: ReversalTransaction
};

export async function HandleTransactionMessage(req: Request, res: Response) {
  const code = Number(req.body['0']);
  if (!code) {
    res.status(400).send('No code received');
    return;
  }

  const handler = transactionHandlers[code];
  if (!handler) {
    LogBadTransactionRequest(req, res, 'code');
    return;
  }

  handler(req, res);
}

// _______ ------Heart------ ____ ------Beat------ _______
export async function HeartBeat(req: Request, res: Response) {
  const bodyJson = req.body;

  if (!bodyJson['0'] || (bodyJson['0'] !== '1804' && bodyJson['0'] !== '1814')) {
    res.status(400).send('Wrong or no code received for heartbeat');
    return;
  }

  // Log data and body
  heartbeatLogger.info(`Card Issuing heartbeat: ${JSON.stringify(bodyJson)}`);

  if (bodyJson['0'] === '1804') {
    // Add the needed response property for heartbeat
    bodyJson['0'] = '1814';
    bodyJson['39'] = '800';
    res.status(200).send(bodyJson);
    heartbeatLogger.info(`Heartbeat response sent: ${JSON.stringify(bodyJson)}`);
  } else if (bodyJson['0'] === '1814') {
    res.status(200).send(bodyJson);
    heartbeatLogger.info(`Heartbeat response sent: ${JSON.stringify(bodyJson)}`);
  }
}

// _______ ------Log------ _______ ------Unexpected------ _______ ------Bad Request------ _______
export async function LogBadTransactionRequest(req: Request, res: Response, input: string = 'body') {
  transactionLogger.info(`Card Issuing unexpected body received: ${JSON.stringify(req.body)}`);
  res.status(400).send(`Webhook received successfully but with unexpected ${input}.`);
}

// --------------------- Request Transaction ---------------------

/**
 * Method used to handle Authorization and Financial Request Transactions
 * @returns Response with status 200 and properly formatted response
 */
async function RequestTransaction(req: Request, res: Response): Promise<void> {
  const body = req.body;
  requestLogger.info(`Card Issuing request transaction: ${JSON.stringify(body)}`);

  // 1 - Validate request and transaction
  const validation = validateTransaction({
    body,
    logger: requestLogger,
    messageType: 'request'
  });

  if (!validation.valid) {
    res.status(400).send(`Webhook received successfully but with invalid request message. ${validation.error}`);
    return;
  }

  let response = validation.response;

  // 1.1 - Validate existing transaction (also if it is return of goods)
  let txValidation;
  const codeStart = body['3'].slice(0, 2);
  const isReturn = codeStart === '20' || codeStart === '52';
  if (isReturn) {
    txValidation = await validateExistingTransaction(body['37'], 'return', requestLogger);
  } else {
    txValidation = await validateExistingTransaction(body['37'], 'request', requestLogger);
  }

  // 1.2 - If validation fail return response
  if (!txValidation.valid) {
    if (txValidation.errorCode) {
      response['39'] = txValidation.errorCode;
    }

    res.status(200).send(response);
    return;
  }

  let tx = undefined;

  try {
    // 1.3 - Validate amounts
    const amountResult = processVisaAmountsWithValidation(body);
    // Check if billing amount is present and what currency is used!
    if (!amountResult.billingAmount) {
      requestLogger.error(`Missing billing amount: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }

    if (!amountResult.isValidConversion) {
      requestLogger.error(`Invalid conversion rate: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }

    const billingAmount: number = amountResult.billingAmount;
    let totalAmount: number = billingAmount;

    // 1.4 - Validate IBAN and Card ID validation and match
    const { message: errorMsg, IBAN } = await checkIBANandCardID(body['102'], body['124']?.['09']);
    if (!IBAN) {
      requestLogger.error(`${errorMsg} for ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    } else if (errorMsg) {
      requestLogger.warn(`${errorMsg} for ${JSON.stringify(body)}`);
    }

    // 1.5 - Validate transaction type
    const txTypeData = mapProcessingCodeToOraSysDetails(body['3']);
    if (!txTypeData) {
      requestLogger.error(`Transaction type not mapped: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }

    let fxFeeAmount = 0;
    // 1.6 - Calculate fees on FX if TX currency is different from billing currency
    if (body['49'] && body['51'] !== body['49']) {
      fxFeeAmount = await ApplyFXFee(body['49'], totalAmount);
      totalAmount += fxFeeAmount;
    }

    // 1.7 - Check if we have a card ID provided
    if (!body['124'] || !body['124']['09']) {
      requestLogger.error(`No card ID provided for credit transaction: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }
    // 1.7.1 - Find the card and account for the account
    const { card, cardAccount, matched } = await getCardAndAccount(body['124']['09'], IBAN);
    if (!card || !cardAccount) {
      requestLogger.error(`Error finding card or account: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    } else if (!matched) {
      requestLogger.error(`Card account number does not match card account number in Mongo: ${JSON.stringify(body)}`);
    }

    // 1.8 - Get needed data for fee calculation
    const operationType = txTypeData.description as OperationType;
    const customerType = cardAccount.customerType as CustomerType;
    const productType = card.productCode as ProductType;
    const location = get2digitsLocationFromField43(body['43']);

    // 1.9 - Calculate normal fees and add them to the object + total amount
    let feeResult;
    if (!location) {
      // 1.9.1 - If no location, find base fee and give warning
      requestLogger.warn(`Location is not found for applying fee: ${JSON.stringify(body)}`);
      feeResult = await ApplyNormalFee(totalAmount, operationType, customerType, productType);
    } else {
      // 1.9.2 - If location, try to find fee for the location!
      feeResult = await ApplyNormalFee(totalAmount, operationType, customerType, productType, location);
      if (!feeResult) {
        // ******* - If cannot find the fee for the location, give warning and find base fee!
        requestLogger.warn(`No fee found for location ${location} in tx: ${JSON.stringify(body)}`);
        feeResult = await ApplyNormalFee(totalAmount, operationType, customerType, productType);
      }
    }
    // 1.9.3 - If not fee, reject tx and send unexpected error email
    if (!feeResult) {
      requestLogger.error(`Cannot find fee for transaction: ${JSON.stringify(body)}`);
      await sendEmailForUnexpectedError([
        {
          header: 'Cannot find fee for transaction',
          message: `${JSON.stringify(body)}`
        }
      ]);
      res.status(200).send(response);
      return;
    }
    totalAmount += feeResult.feeAmount;

    // 2 - Create TX in Mongo
    const formattedData = convertStringFieldsToNamedInterface(body, IBAN);
    tx = await createTransaction(
      formattedData,
      TransactionSourceEnum.ITCard,
      body,
      {
        taxType: feeResult.cardFee.operationType,
        taxAmount: feeResult.feeAmount,
        taxAmountFx: fxFeeAmount,
        taxNarrative: feeResult.cardFee.displayDescriptor
      },
      txTypeData,
      requestLogger
    );
    if (!tx) {
      requestLogger.error(`Error creating transaction in DB: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }

    // 3 - Get currency and available balance
    const balanceResult = await checkBalanceInfoByIBAN(IBAN);
    if (!balanceResult.opCurrency) {
      await denyTransactionWithMessageAndResponse(
        tx._id.toString(),
        `Currency missing for IBAN in OraSys: ${IBAN}`,
        response,
        requestLogger
      );
      requestLogger.error(`Currency missing for IBAN in OraSys: ${IBAN}`);
      res.status(200).send(response);
      return;
    }

    // 4 - Generate auth code and check if it is valid and generate field 54
    const authCode = await generateValidAuthCode(body['37'], IBAN, body['7']);
    if (!authCode) {
      await denyTransactionWithMessageAndResponse(
        tx._id.toString(),
        `Error generating auth code`,
        response,
        requestLogger
      );
      requestLogger.error(`Error generating auth code: ${JSON.stringify(body)}`);
      res.status(200).send(response);
      return;
    }

    // Generate proper field 54, based on the direction type
    const field54 = generateField54(body, txTypeData.directionType === 'C' ? 'C' : 'D', balanceResult);

    // 5.1 - If available amount is 0, resolve transaction
    if (totalAmount === 0) {
      // 5.1.1 - Build approved response
      response = buildApprovedResponseFieldsFinancial(response, authCode, field54);
      // 5.1.2 - Update transaction resolvent with message on success
      await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
        tx._id.toString(),
        true,
        true,
        new Date().toISOString(),
        TransactionResolventTypeEnum.ZeroAmount,
        response,
        'Approved with 0 amount and fee',
        TransactionStatusEnum.Completed,
        requestLogger
      );

      requestLogger.info(
        `Zero amount transaction approved: ${JSON.stringify(body)} with response: ${JSON.stringify(response)}`
      );
      res.status(200).json(response);
      return;
    }

    // 5.2 - Handle credit transactions
    if (txTypeData.directionType === 'C') {
      // 5.2.1 - Type of OCT transactions do not wait for settlement and are directly inserted into OraSys!
      const isOCT = body['3'].slice(0, 2) === '29';
      if (!isOCT) {
        response = buildApprovedResponseFieldsFinancial(response, authCode, field54);
        await updateTransactionProcessingOutcomeWithMessageAndStatus(
          tx._id.toString(),
          true,
          response,
          'Credit transaction registered',
          TransactionStatusEnum.Pending,
          requestLogger
        );
        requestLogger.info(
          `Credit transaction registered: ${JSON.stringify(body)} with response: ${JSON.stringify(response)}`
        );
        res.status(200).json(response);
        return;
      }

      // 5.2.2 - Check if billing amount is less or equal to the fees (to not send 0 tx to OraSys)
      if (billingAmount <= feeResult.feeAmount + fxFeeAmount) {
        requestLogger.error(`Billing amount is less or equal to the fees: ${JSON.stringify(body)}`);
        await denyTransactionWithMessageAndResponse(
          tx._id.toString(),
          `Billing amount is less or equal to the fees`,
          response,
          requestLogger
        );
        res.status(200).send(response);
        return;
      }

      // 5.2.3 - Send data to OraSys
      const dataOraSys = mapToOraSysDataRecordFromCreditTx(
        tx,
        authCode,
        card.cardMask,
        cardAccount.accountHolder,
        IBAN
      );
      const insertingTxResult = await insertOraSysTransaction(dataOraSys);
      if (!insertingTxResult.success) {
        requestLogger.error(
          `Error inserting data to OraSys: ${JSON.stringify(dataOraSys)}, Error: ${insertingTxResult.message}`
        );
        // 5.2.4 - Update transaction resolvent with message on error
        await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
          tx._id.toString(),
          false,
          true,
          new Date().toISOString(),
          TransactionResolventTypeEnum.Denied,
          response,
          `Error inserting settlement data: ${insertingTxResult.message}`,
          TransactionStatusEnum.Declined,
          requestLogger,
          dataOraSys
        );
        res.status(200).send(response);
        return;
      } else {
        // 5.2.5 - Build approved response
        response = buildApprovedResponseFieldsFinancial(response, authCode, field54);

        // 5.2.6 - Update transaction resolvent with message on success
        await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
          tx._id.toString(),
          true,
          true,
          new Date().toISOString(),
          TransactionResolventTypeEnum.Credit,
          response,
          'Credit Transaction Send to OraSys successfully',
          TransactionStatusEnum.Completed,
          requestLogger,
          dataOraSys
        );

        requestLogger.info(
          `OCT credit transaction send to OraSys: ${JSON.stringify(body)} with response: ${JSON.stringify(response)}`
        );
        res.status(200).json(response);
      }

      return;
    }

    // 5.3 - Handle check balance transitions with amount or fee more than 0
    const availableBalance: number = await balanceResult.op_availability;
    const balanceCurrency = await balanceResult.opCurrency;

    // 5.3.1 - Do needed checks for billing amount or transaction amount (Should be always in EURO)
    const checkBilling = isSameCurrencyAndSufficient(
      { amount: availableBalance, currency: balanceCurrency },
      { amount: totalAmount, currency: body['51'] }
    );

    // 5.3.2 - If neither billing nor transaction amount is available, reject transaction
    if (!checkBilling.isValid) {
      if (checkBilling.message === 'Insufficient balance') {
        response['39'] = '116'; // Code 116 = `Denied not sufficient funds`
      } else {
        response['39'] = '130'; // Code 130 = `Denied, currency unacceptable to card issuer`
      }

      requestLogger.info(`${checkBilling.message}: ${JSON.stringify(body)}, response: ${JSON.stringify(response)}`);
      await denyTransactionWithMessageAndResponse(
        tx._id.toString(),
        `${checkBilling.message}`,
        response,
        requestLogger
      );
      res.status(200).send(response);
      return;
    }

    // 5.4 - Neutral/Inquiry transactions are approved without blocking, just checking for sufficient funds
    if (txTypeData.directionType === 'N') {
      // 5.4.1 - Build approved response
      response = buildApprovedResponseFieldsFinancial(response, authCode, field54);

      // 5.4.2 - Update transaction approval code, approvedStatus and resolvent
      await updateTransactionApprovalCode(tx._id.toString(), authCode, requestLogger);
      await updateTransactionProcessingOutcomeWithMessageAndStatusAndResolved(
        tx._id.toString(),
        true,
        true,
        new Date().toISOString(),
        TransactionResolventTypeEnum.Inquiry,
        response,
        'Approved inquiry transaction',
        TransactionStatusEnum.Completed,
        requestLogger
      );

      requestLogger.info(
        `Transaction inquiry approved: ${JSON.stringify(body)} with response: ${JSON.stringify(response)}`
      );
      res.status(200).json(response);
      return;
    }

    // 5.5 - Handle blocking amount and check if it is successful
    const blockFields = mapFieldsFromTxToBlockID(tx, card.cardMask);
    const blockResult = await attemptBlockAmountWithRetries({
      IBAN,
      amount: totalAmount,
      currency: balanceCurrency,
      txId: tx._id.toString(),
      logger: requestLogger,
      bodyOrTx: body,
      authCode,
      extraFields: blockFields
    });
    if (!blockResult.success || !blockResult.blockAmount?.data) {
      await updateTransactionProcessingOutcomeResponse(tx._id.toString(), response, requestLogger);
      res.status(200).send(response);
      return;
    }

    // 5.5.1 - Build approved response and update transaction
    response = buildApprovedResponseFieldsFinancial(response, authCode, field54);
    await updateTransactionProcessingOutcomeResponse(tx._id.toString(), response, requestLogger);

    requestLogger.info(`Transaction approved: ${JSON.stringify(body)} with response: ${JSON.stringify(response)}`);
    res.status(200).json(response);
    return;
  } catch (error) {
    requestLogger.error(`Error occurred while processing transaction: ${JSON.stringify(body)} Error:`, error);
    // 6 - If error, add message to transaction
    if (tx) {
      await addTransactionMessage(tx._id.toString(), `Unexpected Error: ${error}`, requestLogger);
    }
    res.status(200).send(response);
    return;
  }
}

// --------------------- Advice Transaction ---------------------

/**
 * Method used to handle Authorization and Financial Advice Transactions
 * @returns Response with status 200 and properly formatted response
 */
async function AdviceTransaction(req: Request, res: Response): Promise<void> {
  const body = req.body;
  adviceLogger.info(`Card Issuing advice transaction: ${JSON.stringify(body)}`);

  // 1 - Validate request and transaction
  const validation = validateTransaction({
    body,
    logger: adviceLogger,
    messageType: 'advice'
  });

  if (!validation.valid) {
    res.status(200).send('Webhook processed successfully');
    return;
  }

  let response = validation.response;

  // 1.1 - Validate existing transaction
  const txValidation = await validateExistingTransaction(body['37'], 'advice', adviceLogger);
  if (!txValidation.valid) {
    if (txValidation.errorCode) {
      response['39'] = txValidation.errorCode;
    }
    res.status(200).send(response);
    return;
  }

  // 1.2 - Check if type is mapped for advice
  const txTypeData = mapProcessingCodeToOraSysDetails(body['3']);
  if (!txTypeData) {
    requestLogger.warn(`Advice transaction type not mapped: ${JSON.stringify(body)}`);
  }

  // 2 - Create TX in Mongo, add need information for resolvent
  const formattedData = convertStringFieldsToNamedInterface(body);
  const transactionData = {
    ...formattedData,
    messages: [`Advice accepted with action code: ${formattedData.actionCode}`],
    resolvent: {
      resolved: true,
      type: TransactionResolventTypeEnum.Advice,
      date: new Date().toISOString()
    },
    processingOutcome: {
      approved: false,
      responseSend: response
    },
    transStatus: TransactionStatusEnum.Declined
  };

  const tx = await createTransaction(
    transactionData,
    TransactionSourceEnum.ITCard,
    body,
    undefined,
    txTypeData,
    adviceLogger
  );
  if (!tx) {
    // 3 - If no tx, log error
    adviceLogger.error(
      `Error creating advice transaction in DB: ${JSON.stringify(body)}, Response send: ${JSON.stringify(response)}`
    );
    res.status(200).send(response);
    return;
  }

  // 4 - Log and return response
  adviceLogger.info(`Transaction advice handled: ${JSON.stringify(body)}, with response: ${JSON.stringify(response)}`);
  await updateTransactionProcessingOutcomeResponse(tx._id.toString(), response, adviceLogger);
  res.status(200).json(response);
}

// --------------------- Reversal Transaction ---------------------

/**
 * Method used to handle Reversal Advice Transactions
 * @returns Response with status 200 and properly formatted response
 */
async function ReversalTransaction(req: Request, res: Response): Promise<void> {
  const body = req.body;
  reversalLogger.info(`Card Issuing reversal transaction: ${JSON.stringify(body)}`);

  // 1 - Validate request and transaction
  const validation = validateTransaction({
    body,
    logger: reversalLogger,
    messageType: 'reversal'
  });

  if (!validation.valid) {
    res.status(200).send('Webhook processed successfully');
    return;
  }

  let response = validation.response;

  // 2 - Validate existing transaction
  const txValidation = await validateExistingTransaction(body['37'], 'reversal', reversalLogger);
  if (!txValidation.valid) {
    if (txValidation.errorCode) {
      response['39'] = txValidation.errorCode;
    }
    if (txValidation.response && txValidation.response._id) {
      await updateTransactionReversalWithMessage(
        txValidation.response._id.toString(),
        false,
        body,
        response,
        `Reversal received, but hit an error: ${txValidation.error}`,
        reversalLogger
      );
    }
    res.status(200).send(response);
    return;
  }

  const existingTx = txValidation.response;

  // 3 - Validate reversal details
  // @Notice: For now we check if the amount is the same and we do not handle partial reversals!
  // In future we can block amount - reversal, then unblock full amount and get the desired result, but we also need to keep track of the settlement differently!
  const reversalDetailsValidation = validateReversalDetails(existingTx, body, reversalLogger);
  if (!reversalDetailsValidation.valid) {
    await updateTransactionReversalWithMessage(
      txValidation.response._id.toString(),
      false,
      body,
      response,
      `Reversal received, but validation failed: ${reversalDetailsValidation.error}`,
      reversalLogger
    );
    res.status(200).send(response);

    // 3.1 - If transaction already resolved (but not reverted, cuz we can receive this 3 times in row), send email
    if (reversalDetailsValidation.error === 'Transaction already resolved') {
      await sendEmailForUnexpectedError([
        {
          header: 'Reversal received for already resolved transaction',
          message: `${JSON.stringify(body)}`
        }
      ]);
    }
    return;
  }

  const currentTime = new Date();

  // 4 - If no block id, mark as resolved and revert
  if (!existingTx.blockId) {
    response['39'] = '400'; // Code 400 = `Reversal accepted`
    await updateTransactionReversalWithStatusAndResolventWithMessage(
      existingTx._id.toString(),
      true,
      TransactionStatusEnum.Reverted,
      true,
      TransactionResolventTypeEnum.Reversal,
      currentTime.toString(),
      'Transaction with no block id reversed successfully!',
      reversalLogger,
      body,
      response
    );
    reversalLogger.info(`Transaction reversed: ${JSON.stringify(body)}, response: ${JSON.stringify(response)}`);
    res.status(200).send(response);
    return;
  }

  // 5 - Unblock amount from OraSys
  const unblockAmount = await unblockAmountByBlockID(existingTx.blockId);
  if (!unblockAmount || unblockAmount.v_res !== existingTx.blockId || unblockAmount.v_res === 0) {
    // 5.1 - If error, update transaction resolvent with message
    await updateTransactionReversalWithMessage(
      existingTx._id.toString(),
      false,
      body,
      response,
      `Unblocking amount failed: ${JSON.stringify(unblockAmount)}`,
      reversalLogger
    );
    // Log error and return response
    reversalLogger.error(
      `Unblocking amount failed: ${JSON.stringify(unblockAmount)} for ${JSON.stringify(body)}, response: ${JSON.stringify(response)}`
    );
    res.status(200).send(response);
    return;
  } else {
    // 5.2 - Build the successful response and update transaction
    response['39'] = '400'; // Code 400 = `Reversal accepted`

    await updateTransactionReversalWithStatusAndResolventWithMessage(
      existingTx._id.toString(),
      true,
      TransactionStatusEnum.Reverted,
      true,
      TransactionResolventTypeEnum.Reversal,
      currentTime.toString(),
      `Unblocking full amount successful: ${JSON.stringify(unblockAmount)}`,
      reversalLogger,
      body,
      response
    );
    // Log and return response
    reversalLogger.info(`Transaction reversed: ${JSON.stringify(body)}, response: ${JSON.stringify(response)}`);
    res.status(200).send(response);
  }
}
