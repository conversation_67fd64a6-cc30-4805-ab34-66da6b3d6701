import express from 'express';

import { HandleTransactionMessage } from '../controllers/transactionController';
import {
  InsertTxWithApplyingFee,
  GetTransactionFee,
  GetAvailableBalance,
  GetTransactions,
  InsertTransactionFee,
  RegisterCard
} from '../controllers/cipController';
import { searchCardFees, searchFXRates } from '../controllers/cardFeeController';
import { authenticatePartnerJWT } from '../middlewares/partnerVerification';

const partnersRouter = express.Router();

// _________ ------------ IT Card ------------ _________
// @Todo: The old endpoint, remove after confirming ITCard uses the transaction-message endpoint & add Auth when is set by ITCard
partnersRouter.post('/api/itcard/transaction-authorization-messages', HandleTransactionMessage);
partnersRouter.post('/api/itcard/transaction-message', HandleTransactionMessage);

// _________ ------------ CIP ------------ _________
partnersRouter.get('/api/itcard/transactions', authenticatePartnerJWT(), GetTransactions);
partnersRouter.get('/api/itcard/balance-check', authenticatePartnerJWT(), GetAvailableBalance);
partnersRouter.post('/api/cip/register-card', authenticatePartnerJWT(), RegisterCard);
partnersRouter.post('/api/cip/fee-transaction', authenticatePartnerJWT(), InsertTransactionFee);
partnersRouter.post('/api/cip/check-tx-fee', authenticatePartnerJWT(), GetTransactionFee);
partnersRouter.post('/api/cip/apply-tx-fee', authenticatePartnerJWT(), InsertTxWithApplyingFee);

// Get fees & rates endpoints for partners
partnersRouter.get('/api/card-fees', authenticatePartnerJWT(), searchCardFees);
partnersRouter.get('/api/fx-rates', authenticatePartnerJWT(), searchFXRates);

export default partnersRouter;
