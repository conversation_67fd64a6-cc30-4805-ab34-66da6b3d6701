import express from 'express';

import logger from '../utils/logger';
import { cardFeeSchema, fxRateSchema } from '../schemaValidations/FeeSchemaValidation';
import { CreatePartner } from '../controllers/partnersController';
import {
  searchCardFees,
  createNewCardFee,
  createNewFXRate,
  searchFXRates,
  createBulkFXRates,
  createBulkCardFees
} from '../controllers/cardFeeController';
import { cleanUploads, uploadExcel, uploadXML } from '../middlewares/fileHandler';
import { verifyIP } from '@submodules/ryvyl-commons/middlewares/whitelistMiddleware';
import { authenticateJWT } from '@submodules/ryvyl-commons/middlewares/authMiddleware';
import validateRequest from '@submodules/ryvyl-commons/middlewares/validateReqMiddleware';
import { ParseAndSettleXmlFile } from '../controllers/settlementController';
import { authorizeCIPLogin } from '../middlewares/CIPLogin';
import { handleCIPClient } from '../controllers/cipController';

const routes = express.Router();

// Health-check endpoint
routes.get('', function (_req: express.Request, res: express.Response) {
  logger.info(`Health-check probe/readiness: ${Date.now()}`);
  res.status(200).send('OK');
});

// ---------------- Settlement endpoints ----------------

routes.post('/upload-xml', verifyIP, authenticateJWT, uploadXML.any(), ParseAndSettleXmlFile, cleanUploads);

// ---------------- Partners endpoints ----------------

// Create Partner endpoint
routes.post('/api/create-partner', verifyIP, authenticateJWT, CreatePartner);

// We call to create debit account and 1st virtual card in CIP:
routes.post('/api/cip/handle-cip', verifyIP, authenticateJWT, authorizeCIPLogin, handleCIPClient);

// ---------------- Card Fee endpoints ----------------

// Get card fees and fx rates
routes.get('/api/card-fees', verifyIP, authenticateJWT, searchCardFees);
routes.get('/api/fx-rates', verifyIP, authenticateJWT, searchFXRates);
// Create a single fee or rate
routes.post('/api/create-card-fee', verifyIP, authenticateJWT, validateRequest(cardFeeSchema), createNewCardFee);
routes.post('/api/create-fx-rate', verifyIP, authenticateJWT, validateRequest(fxRateSchema), createNewFXRate);
// Create multiple fees or rates
routes.post(
  '/api/create-bulk-card-fees',
  verifyIP,
  authenticateJWT,
  uploadExcel.any(),
  createBulkCardFees,
  cleanUploads
);
routes.post('/api/create-bulk-fx-rates', verifyIP, authenticateJWT, createBulkFXRates);

export default routes;
