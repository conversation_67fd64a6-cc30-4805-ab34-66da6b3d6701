import SFTPClient from 'ssh2-sftp-client';
import fs from 'fs/promises';
import path from 'path';
import { SFTP_Configs } from '../config';
import { sftpLogger } from '../utils/logger';

export type SFTPAction =
  | { action: 'download'; remotePath: string; localPath: string }
  | { action: 'upload'; localPath: string; remotePath: string }
  | { action: 'upload-dir'; localDir: string; remoteDir: string }
  | { action: 'upload-to-dir'; localPath: string; remoteDir: string }
  | { action: 'upload-selected-files'; localDir: string; remoteDir: string; fileNames: string[] }
  | { action: 'delete'; remotePath: string }
  | { action: 'delete-files'; remoteDir: string; fileNames: string[] }
  | { action: 'list'; remoteDir: string }
  | { action: 'exists'; remotePath: string }
  | { action: 'download-all-xml'; remoteDir: string; localDir: string };

/**
 * Handle SFTP tasks based on the provided configuration key and action.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 * @returns Promise that resolves when the task is completed.
 */
export async function handleSFTP(configKey: keyof typeof SFTP_Configs, task: SFTPAction) {
  const sftp = new SFTPClient();
  const config = SFTP_Configs[configKey];

  sftp.on('error', (err) => {
    sftpLogger.error(`[${configKey}] SFTP error:`, err.message);
    sftpLogger.error(`[${configKey}] Stack:`, err.stack);
  });

  try {
    sftpLogger.info(`[${configKey}] Connecting to SFTP...`);
    await sftp.connect({
      ...config,
      algorithms: {
        serverHostKey: ['ssh-ed25519', 'ecdsa-sha2-nistp256', 'ssh-rsa']
      },
      readyTimeout: 20000
    });

    const actionHandlers: Record<string, Function> = {
      download: handleDownload,
      upload: handleUpload,
      delete: handleDelete,
      list: handleList,
      exists: handleExists,
      'upload-dir': handleUploadAllFilesInDir,
      'upload-to-dir': handleUploadFileToDir,
      'upload-selected-files': handleUploadSelectedFiles,
      'delete-files': deleteFilesInRemoteDir,
      'download-all-xml': handleDownloadAllXml
    };

    const handler = actionHandlers[task.action];
    if (!handler) throw new Error(`Unknown SFTP action: ${task.action}`);

    return await handler(sftp, configKey, task);
  } catch (err) {
    sftpLogger.error(`[${configKey}] SFTP Error:`, err);
    throw err;
  } finally {
    await sftp.end();
    sftpLogger.info(`[${configKey}] SFTP Connection closed.`);
  }
}

// _____________-------------- Download --------------_____________
/**
 * Downloads a file from the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleDownload(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'download') return;
  const { remotePath, localPath } = task;
  const exists = await sftp.exists(remotePath);
  if (exists !== '-') {
    sftpLogger.warn(`[${configKey}] Remote file not found: ${remotePath}`);
    return;
  }

  try {
    await fs.access(localPath);
    sftpLogger.warn(`[${configKey}] Skipped download, file already exists locally: ${localPath}`);
    return;
  } catch {}

  try {
    await fs.mkdir(path.dirname(localPath), { recursive: true });
    await sftp.get(remotePath, localPath);
    sftpLogger.info(`[${configKey}] Downloaded: ${remotePath} → ${localPath}`);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Download failed: ${remotePath} → ${localPath}`, err);
  }
}

// _____________-------------- Upload --------------_____________
/**
 * Uploads a file to the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleUpload(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'upload') return;
  const { localPath, remotePath } = task;
  try {
    await fs.access(localPath);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Upload skipped — local file not found: ${localPath}`);
    return;
  }

  const exists = await sftp.exists(remotePath);
  if (exists === '-') {
    sftpLogger.warn(`[${configKey}] Skipped upload, file already exists remotely: ${remotePath}`);
    return;
  }

  try {
    const remoteDir = path.posix.dirname(remotePath);
    await sftp.mkdir(remoteDir, true);
    await sftp.put(localPath, remotePath);
    sftpLogger.info(`[${configKey}] Uploaded: ${localPath} → ${remotePath}`);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Upload failed: ${localPath} → ${remotePath}`, err);
  }
}

/**
 * Uploads a specific file to a remote directory.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleUploadFileToDir(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'upload-to-dir') return;

  const { localPath, remoteDir } = task;
  const fileName = path.basename(localPath);
  const remotePath = path.posix.join(remoteDir, fileName);

  try {
    await fs.access(localPath);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Upload skipped — local file not found: ${localPath}`);
    return;
  }

  const exists = await sftp.exists(remotePath);
  if (exists === '-') {
    sftpLogger.warn(`[${configKey}] Skipped upload, file already exists remotely: ${remotePath}`);
    return;
  }

  try {
    await sftp.mkdir(remoteDir, true);
    await sftp.put(localPath, remotePath);
    sftpLogger.info(`[${configKey}] Uploaded: ${localPath} → ${remotePath}`);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Upload failed: ${localPath} → ${remotePath}`, err);
  }
}

/**
 * Uploads all files from a local directory to a remote directory on the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleUploadAllFilesInDir(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'upload-dir') return;
  const { localDir, remoteDir } = task;
  try {
    const entries = await fs.readdir(localDir, { withFileTypes: true });
    const files = entries.filter((entry) => entry.isFile());

    if (files.length === 0) {
      sftpLogger.info(`[${configKey}] No files to upload in local dir: ${localDir}`);
      return;
    }

    await sftp.mkdir(remoteDir, true);

    for (const file of files) {
      const localPath = path.join(localDir, file.name);
      const remotePath = path.posix.join(remoteDir, file.name);

      try {
        const exists = await sftp.exists(remotePath);
        if (exists === '-') {
          sftpLogger.warn(`[${configKey}] Skipped upload, file already exists remotely: ${remotePath}`);
          continue;
        }

        await sftp.put(localPath, remotePath);
        sftpLogger.info(`[${configKey}] Uploaded: ${localPath} → ${remotePath}`);
      } catch (err) {
        sftpLogger.error(`[${configKey}] Failed to upload ${file.name}`, err);
      }
    }
  } catch (err) {
    sftpLogger.error(`[${configKey}] Failed to upload directory: ${localDir}`, err);
  }
}

/**
 * Uploads specific files from a local directory to a remote directory on the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
export async function handleUploadSelectedFiles(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'upload-selected-files') return;
  const { localDir, remoteDir, fileNames } = task;

  if (fileNames.length === 0) {
    sftpLogger.info(`[${configKey}] No files to upload.`);
    return;
  }

  try {
    await sftp.mkdir(remoteDir, true);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Failed to ensure remote directory exists: ${remoteDir}`, err);
    return;
  }

  for (const fileName of fileNames) {
    const localPath = path.join(localDir, fileName);
    const remotePath = path.posix.join(remoteDir, fileName);

    try {
      await fs.access(localPath); // ensure file exists
    } catch {
      sftpLogger.warn(`[${configKey}] Skipping upload, local file not found: ${localPath}`);
      continue;
    }

    try {
      const exists = await sftp.exists(remotePath);
      if (exists === '-') {
        sftpLogger.info(`[${configKey}] Skipped upload, file already exists remotely: ${remotePath}`);
        continue;
      }

      await sftp.put(localPath, remotePath);
      sftpLogger.info(`[${configKey}] Uploaded: ${localPath} → ${remotePath}`);
    } catch (err) {
      sftpLogger.error(`[${configKey}] Failed to upload ${fileName}`, err);
    }
  }
}

// _____________-------------- Delete --------------_____________
/**
 * Deletes a file from the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleDelete(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'delete') return;
  const exists = await sftp.exists(task.remotePath);
  if (!exists) {
    sftpLogger.error(`[${configKey}] File not found, cannot delete: ${task.remotePath}`);
    return;
  }
  if (exists !== '-') {
    sftpLogger.warn(`[${configKey}] Not a file, skipping delete: ${task.remotePath}`);
    return;
  }

  try {
    await sftp.delete(task.remotePath);
    sftpLogger.info(`[${configKey}] Deleted file: ${task.remotePath}`);
  } catch (err) {
    sftpLogger.error(`[${configKey}] Delete failed: ${task.remotePath}`, err);
  }
}

/**
 * Deletes multiple files from a specific directory on the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
export async function deleteFilesInRemoteDir(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'delete-files') return;
  const { remoteDir, fileNames } = task;
  for (const fileName of fileNames) {
    const remotePath = path.posix.join(remoteDir, fileName);

    try {
      const exists = await sftp.exists(remotePath);

      if (!exists) {
        sftpLogger.error(`[${configKey}] File not found, cannot delete: ${remotePath}`);
        continue;
      }

      if (exists !== '-') {
        sftpLogger.warn(`[${configKey}] Not a file, skipping delete: ${remotePath}`);
        continue;
      }

      await sftp.delete(remotePath);
      sftpLogger.info(`[${configKey}] Deleted file: ${remotePath}`);
    } catch (err) {
      sftpLogger.error(`[${configKey}] Failed to delete file: ${remotePath}`, err);
    }
  }
}

// _____________-------------- Listing --------------_____________
/**
 * Lists the contents of a directory on the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleList(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'list') return;
  sftpLogger.info(`[${configKey}] Listing directory: ${task.remoteDir}`);
  try {
    const list = await sftp.list(task.remoteDir);
    const names = list.map((item) => item.name);
    sftpLogger.info(`[${configKey}] Items: ${JSON.stringify(names)}`);
    return list;
  } catch (err) {
    sftpLogger.error(`[${configKey}] Failed to list directory: ${task.remoteDir}`, err);
  }
}

// _____________-------------- Exists --------------_____________
/**
 * Checks if a file or directory exists on the SFTP server.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleExists(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'exists') return;
  const exists = await sftp.exists(task.remotePath);
  let message = '';
  switch (exists) {
    case 'd':
      message = 'It is a directory';
      break;
    case '-':
      message = 'It is a file';
      break;
    case 'l':
      message = 'It is a symlink';
      break;
    default:
      message = 'It does not exist';
      break;
  }
  sftpLogger.info(`[${configKey}] Exists check: ${task.remotePath} => ${message}`);
  return exists;
}

// _____________-------------- Download All XML --------------_____________
/**
 * Downloads all XML files from a directory on the SFTP server, skipping ones that already exist locally.
 * @param sftp - The SFTP client instance.
 * @param configKey - The key of the SFTP configuration to use.
 * @param task - The SFTP action to perform.
 */
async function handleDownloadAllXml(sftp: SFTPClient, configKey: string, task: SFTPAction) {
  if (task.action !== 'download-all-xml') return;
  const { remoteDir, localDir } = task;

  const exists = await sftp.exists(remoteDir);
  if (!exists) {
    sftpLogger.error(`[${configKey}] Remote dir not found: ${remoteDir}`);
    return;
  }

  try {
    const fileList = await sftp.list(remoteDir);
    const xmlFiles = fileList.filter((f) => f.name.toLowerCase().endsWith('.xml'));

    if (xmlFiles.length === 0) {
      sftpLogger.warn(`[${configKey}] No XML files in ${remoteDir}`);
      return;
    }

    await fs.mkdir(localDir, { recursive: true });

    for (const file of xmlFiles) {
      const remotePath = `${remoteDir}/${file.name}`;
      const localPath = `${localDir}/${file.name}`;

      try {
        // Check if file already exists locally
        await fs.access(localPath);
        sftpLogger.warn(`[${configKey}] Skipped download, file already exists: ${file.name}`);
        continue;
      } catch {
        // File does not exist — proceed to download
      }

      try {
        await sftp.get(remotePath, localPath);
        sftpLogger.info(`[${configKey}] Downloaded: ${file.name}`);
      } catch (err) {
        sftpLogger.error(`[${configKey}] Failed to download: ${file.name}`, err);
      }
    }
  } catch (err) {
    sftpLogger.error(`[${configKey}] Failed to download all XMLs from ${remoteDir}`, err);
  }
}
