import { makeBackendRequest } from '../utils/helpers';
import { Request, Response, NextFunction } from 'express';
import { CIPConfig } from '../config';
import { CIPLogger } from '../utils/logger';

export async function authorizeCIPLogin(req: Request, res: Response, next: NextFunction) {
  // Build the body
  const body = {
    email: process.env.CIP_EMAIL,
    password: process.env.CIP_PASSWORD
  };

  try {
    // Send request for login to CIP:
    const response = await makeBackendRequest({
      method: 'POST',
      url: CIPConfig.ryvylCIPUrl + '/api/auth/login',
      data: body,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.status !== 200) {
      CIPLogger.error(`Error logging in to CIP: ${JSON.stringify(response)}`);
      res.status(400).send(response);
      return;
    }

    req.headers.authorization = 'Bearer ' + response.data.token;
    next();
  } catch (error) {
    CIPLogger.error(error);
    res.status(500).send(error);
  }
}
