import multer from 'multer';
import fs from 'fs/promises';
import path from 'path';
import { fileLogger } from '../utils/logger';

/**
 * Upload XML files middleware.
 */
export const uploadXML = multer({
  dest: 'uploads/',
  limits: { fileSize: 5 * 1024 * 1024 }, // Max 5MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['application/xml', 'text/xml', 'text/plain'];
    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Invalid file type. Only XML files are allowed.'));
    }
    cb(null, true);
  }
});

/**
 * Upload Excel files middleware.
 */
export const uploadExcel = multer({
  dest: 'uploads/',
  limits: { fileSize: 5 * 1024 * 1024 }, // 5MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/csv' // alternative CSV mime type
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Invalid file type. Only Excel (.xlsx, .xls) and CSV files are allowed.'));
    }
    cb(null, true);
  }
});

// _________ -------- Clean Uploads -------- _________

export async function cleanUploads() {
  const dir = path.join(__dirname, '../../uploads');
  try {
    const files = await fs.readdir(dir);
    if (files.length === 0) {
      fileLogger.info('No leftover uploads to clean up');
      return;
    }
    for (const file of files) {
      await fs.unlink(path.join(dir, file));
    }
    fileLogger.info('Cleaned up leftover uploads');
  } catch (err) {
    fileLogger.warn('Could not clean uploads folder:', err);
  }
}
