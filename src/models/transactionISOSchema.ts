import { Schema, model } from 'mongoose';
import {
  TransactionInterfaceData,
  TransactionResolventTypeEnum,
  TransactionSourceEnum,
  TransactionStatusEnum
} from '../interfaces/transactionInterface';
import { TransactionTypeData } from '../constants/transactionType';

const transTypeSchema = new Schema<TransactionTypeData>(
  {
    codeOraSys: { type: String, required: true },
    description: { type: String, required: true },
    action: {
      type: String,
      enum: ['DR', 'CR', 'INQ', 'ADM', 'PS', 'EWT'],
      required: true
    },
    directionType: {
      type: String,
      enum: ['C', 'D', 'N'],
      required: true
    },
    ryvylLabel: { type: String, required: true },
    ryvylStatement: { type: String, required: true }
  },
  { _id: false }
);

const ISOTransactionModel = new Schema<TransactionInterfaceData>(
  {
    messageTypeIndicator: { type: String, required: true },
    primaryAccountNumber: String,
    processingCode: { type: Schema.Types.Mixed },
    transactionAmount: Number,
    reconciliationAmount: Number,
    cardholderBillingAmount: Number,
    transmissionDateTime: String,
    reconciliationConversionRate: String,
    cardholderBillingConversionRate: String,
    stan: { type: String, sparse: true, index: true },
    localTransactionTime: String,
    expirationDate: String,
    reconciliationDate: String,
    conversionDate: String,
    merchantCategoryCode: String,
    acquiringInstitutionCountryCode: String,
    pointOfServiceDataCode: { type: Schema.Types.Mixed },
    cardSequenceNumber: String,
    functionCode: String,
    messageReasonCode: String,
    cardAcceptorBusinessCode: String,
    amountsOriginal: String,
    acquiringInstitutionId: String,
    forwardingInstitutionId: String,
    track2Data: String,
    track3Data: String,
    retrievalReferenceNumber: { type: String, index: true }, // Should be unique, unless Return of Goods (20) tx come for existing tx
    approvalCode: { type: String, sparse: true, index: true }, // Should be as unique as possible, but could have duplicates by docs
    actionCode: String,
    cardAcceptorTerminalId: String,
    cardAcceptorIdCode: String,
    cardAcceptorNameLocation: { type: Schema.Types.Mixed },
    additionalResponseData: String,
    track1Data: String,
    amountsFeesISO: String,
    transactionCurrencyCode: String,
    reconciliationCurrencyCode: String,
    cardholderBillingCurrencyCode: String,
    pinData: String,
    amountsAdditional: String,
    iccData: String,
    originalDataElements: { type: Schema.Types.Mixed },
    authorizationLifeCycleCode: String,
    authorizingAgentInstitutionIdCode: String,
    reservedPrivate: { type: Schema.Types.Mixed },
    dataRecord: String,
    transactionDestinationInstitutionIdCode: String,
    transactionDestinationOriginatorIdCode: String,
    accountIdentification1: { type: String, sparse: true, index: true },
    reservedForPrivateUse: { type: Schema.Types.Mixed },
    cardId: { type: String },
    // Data fields
    blockId: { type: Number, unique: true, sparse: true, index: true },
    initialBodyData: { type: Schema.Types.Mixed, required: true },
    messages: { type: [String], default: [] },
    // Inline processing outcome
    processingOutcome: {
      approved: { type: Boolean, default: false },
      responseSend: { type: Schema.Types.Mixed },
      txDataSendToOraSys: { type: Schema.Types.Mixed },
      blockDataSendToOraSys: { type: Schema.Types.Mixed }
    },
    // Reversal by IT Card or Cronjob
    reversal: {
      reversed: { type: Boolean, default: false },
      reversalData: { type: Schema.Types.Mixed },
      responseSend: { type: Schema.Types.Mixed }
    },
    // Settlement by VisaNet
    settlement: {
      settled: { type: Boolean, default: false },
      settlementData: { type: Schema.Types.Mixed },
      dataSendToOraSys: { type: Schema.Types.Mixed }
    },
    // Resolvent information
    resolvent: {
      type: {
        type: String,
        enum: Object.values(TransactionResolventTypeEnum),
        default: TransactionResolventTypeEnum.Unknown
      },
      resolved: { type: Boolean, default: false },
      date: { type: String }
    },
    // Source of the transaction
    source: {
      type: String,
      enum: Object.values(TransactionSourceEnum),
      default: TransactionSourceEnum.Unknown
    },
    // Transaction status
    transStatus: {
      type: String,
      enum: Object.values(TransactionStatusEnum),
      default: TransactionStatusEnum.Declined
    },
    // Transaction type
    transType: {
      type: transTypeSchema,
      required: false
    },
    // Fees of the transaction
    fees: {
      taxType: { type: String },
      taxAmount: { type: Number, default: 0 },
      taxAmountFx: { type: Number, default: 0 },
      taxNarrative: { type: String }
    }
  },
  {
    timestamps: true
  }
);

export const Iso8583Transaction = model<TransactionInterfaceData>('Iso8583Transaction', ISOTransactionModel);
