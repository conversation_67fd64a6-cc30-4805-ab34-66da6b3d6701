import mongoose, { Schema } from 'mongoose';
import { AccountInterface, AccountStatus } from '../interfaces/debitInterface';
import { CustomerType } from '../constants/fees';

const ownerSchema = new Schema(
  {
    clientCode: { type: String, required: true },
    relationship: { type: String, required: true },
    mainOwner: { type: Boolean, required: true },
    _id: { type: String, required: true }
  },
  { _id: false }
);

const accountSchema = new Schema<AccountInterface>(
  {
    owners: [ownerSchema],
    customerType: { type: String, required: true, enum: Object.values(CustomerType) },
    onboarding: { type: String, required: true },
    accountNumber: { type: String, required: true, maxlength: 26 },
    accountHolder: { type: String, required: true, maxlength: 40 },
    currencyCode: { type: String, required: true, maxlength: 3 },
    currency: { type: String, required: true },
    relationship: { type: String, required: true },
    bankName: { type: String, required: true, maxlength: 30 },
    status: {
      type: String,
      required: true,
      enum: Object.values(AccountStatus),
      maxlength: 6
    },
    clientCode: { type: String, required: true },
    extAppId: { type: String, required: false, maxlength: 40 },
    productCode: { type: String, required: true, maxlength: 4 },
    productDesc: { type: String, required: true, maxlength: 225 },
    bankNumber: { type: String, required: true, maxlength: 20 },
    openDate: { type: String, required: false },
    currencyName: { type: String, required: true },
    balance: { type: Number, required: true }
  },
  { timestamps: true }
);

const DebitAccount = mongoose.model<AccountInterface>('DebitAccount', accountSchema);

export default DebitAccount;
