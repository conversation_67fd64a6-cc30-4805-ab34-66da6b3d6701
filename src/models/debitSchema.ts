import mongoose, { Schema } from 'mongoose';
import { AccountInterface, AccountStatus } from '../interfaces/debitInterface';
import { CustomerType } from '../constants/fees';

// Owner Sub-schema
const ownerSchema = new Schema(
  {
    clientCode: { type: String, required: true },
    relationship: { type: String, required: true },
    mainOwner: { type: Boolean, required: true },
    _id: { type: String, required: true }
  },
  { _id: false }
);

const accountSchema = new Schema<AccountInterface>(
  {
    owners: [ownerSchema],
    customerType: { type: String, required: true, enum: Object.values(CustomerType) },
    onboarding: { type: String, required: true },
    isCompany: { type: Boolean, required: false },
    isB2b: { type: Boolean, required: false },
    accountNumber: { type: String, required: true },
    accountHolder: { type: String, required: true },
    currencyCode: { type: String, required: true },
    relationship: { type: String, required: true },
    bankName: { type: String, required: true },
    status: {
      type: String,
      required: true,
      enum: Object.values(AccountStatus)
    },
    clientCode: { type: String, required: true },
    extAppId: { type: String, required: false },
    productCode: { type: String, required: true },
    productDesc: { type: String, required: true },
    bankNumber: { type: String, required: true },
    openDate: { type: String, required: false },
    currency: { type: String, required: true },
    balance: { type: Number, required: true }
  },
  { timestamps: true }
);

const DebitAccount = mongoose.model<AccountInterface>('DebitAccount', accountSchema);

export default DebitAccount;
