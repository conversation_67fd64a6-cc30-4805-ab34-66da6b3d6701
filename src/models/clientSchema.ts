import mongoose, { Schema } from 'mongoose';

import { ClientInterface, CustomerIdType, RiskStatus, ApplicationStatus } from '../interfaces/clientInterface';

// Personal Info Sub-schema
const personalInfoSchema = new Schema(
  {
    firstName: { type: String, required: true },
    middleName: { type: String, required: false },
    lastName: { type: String, required: true },
    mothersMaidenName: { type: String, required: false },
    email: { type: String, required: true },
    phone: { type: String, required: true },
    authPhoneNumber: { type: String, required: true },
    dateOfBirth: { type: String, required: true },
    birthCountry: { type: String, required: true }
  },
  { _id: false }
);

// Address Sub-schema
const addressSchema = new Schema(
  {
    street: { type: String, required: true },
    buildingNumber: { type: String, required: false },
    apartmentNumber: { type: String, required: false },
    city: { type: String, required: true },
    stateProvince: { type: String, required: false },
    zipCode: { type: String, required: true },
    country: { type: String, required: true }
  },
  { _id: false }
);

// ID Document Sub-schema
const idDocumentSchema = new Schema(
  {
    customerIdType: {
      type: String,
      required: true,
      enum: Object.values(CustomerIdType)
    },
    number: { type: String, required: true },
    issueDate: { type: Date, required: true },
    expiryDate: { type: Date, required: true },
    issuingCountry: { type: String, required: true },
    idAuthority: { type: String, required: true }
  },
  { _id: false }
);

// Tax Info Sub-schema
const taxInfoSchema = new Schema(
  {
    country: { type: String, required: false },
    taxIdNumber: { type: String, required: false }
  },
  { _id: false }
);

const clientSchema = new Schema<ClientInterface>(
  {
    origin: { type: String, required: true },
    company: { type: String, required: true },
    productVersion: [{ type: String, required: true }],
    personalInfo: { type: personalInfoSchema, required: true },
    address: { type: addressSchema, required: true },
    operationStatus: { type: String, required: true },
    idDocument: { type: idDocumentSchema, required: true },
    taxInfo: { type: taxInfoSchema, required: false },
    clientID: { type: String, required: true, unique: true },
    cardTypes: [{ type: String }],
    cardCurrency: [{ type: String }],
    client: { type: Boolean, required: true },
    permissions: [{ type: String }],
    legalId: { type: String, required: false },
    citizenship: { type: String, required: true },
    applicationId: { type: String, required: true },
    riskLevel: { type: Number, required: true },
    riskStatus: {
      type: String,
      required: true,
      enum: Object.values(RiskStatus)
    },
    applicationStatus: {
      type: String,
      required: true,
      enum: Object.values(ApplicationStatus)
    },
    applicationDate: { type: String, required: true },
    dashboardStatus: { type: String, required: true }
  },
  { timestamps: true }
);

const Client = mongoose.model<ClientInterface>('Client', clientSchema);

export default Client;
