import mongoose, { Schema } from 'mongoose';

import { ClientInterface, CustomerIdType, RiskStatus, ApplicationStatus } from '../interfaces/clientInterface';

const clientSchema = new Schema<ClientInterface>(
  {
    origin: { type: String, required: true },
    company: { type: String, required: true },
    productVersion: [{ type: String, required: true }],
    personalInfo: {
      firstName: { type: String, required: true, maxlength: 40 },
      secondName: { type: String, maxlength: 40 },
      lastName: { type: String, required: true, maxlength: 40 },
      mothersMaidenName: { type: String, maxlength: 40 },
      email: { type: String, required: true, maxlength: 40 },
      phoneNumber: { type: String, required: true, maxlength: 16 },
      authPhoneNumber: { type: String, required: true, maxlength: 16 },
      birthDate: { type: String, required: true },
      birthCountry: { type: String, required: true, maxlength: 40 }
    },
    address: {
      street: { type: String, required: true, maxlength: 40 },
      buildingNumber: { type: String, required: false, maxlength: 40 },
      apartmentNumber: { type: String, required: false, maxlength: 10 },
      city: { type: String, required: true, maxlength: 40 },
      stateProvince: { type: String, required: false, maxlength: 40 },
      zipCode: { type: String, required: true, maxlength: 10 },
      country: { type: String, required: true, maxlength: 40 }
    },
    operationStatus: { type: String, required: true },
    idDocument: {
      customerIdType: {
        type: String,
        required: true,
        enum: Object.values(CustomerIdType),
        maxlength: 20
      },
      number: { type: String, required: true, maxlength: 20 },
      issueDate: { type: Date, required: true },
      expiryDate: { type: Date, required: true },
      issuingCountry: { type: String, required: true, maxlength: 40 },
      idAuthority: { type: String, required: true, maxlength: 40 }
    },
    taxInfo: {
      country: { type: String, required: false, maxlength: 2 },
      taxIdNumber: { type: String, required: false, maxlength: 40 }
    },
    clientID: { type: String, required: true, unique: true, maxlength: 15 },
    cardTypes: [{ type: String }],
    cardCurrency: [{ type: String }],
    client: { type: Boolean, required: true },
    legalId: { type: String, required: false, maxlength: 11 },
    citizenship: { type: String, required: true, maxlength: 40 },
    applicationId: { type: String, required: true, maxlength: 40 },
    riskLevel: { type: Number, required: true, min: 0, max: 999 },
    riskStatus: {
      type: String,
      required: true,
      enum: Object.values(RiskStatus),
      maxlength: 9
    },
    applicationStatus: {
      type: String,
      required: true,
      enum: Object.values(ApplicationStatus),
      maxlength: 10
    },
    applicationDate: { type: String, required: true, maxlength: 22 },
    dashboardStatus: { type: String, required: true }
  },
  { timestamps: true }
);

const Client = mongoose.model<ClientInterface>('Client', clientSchema);

export default Client;
