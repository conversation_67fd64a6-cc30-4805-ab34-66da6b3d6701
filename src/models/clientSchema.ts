import mongoose, { Schema } from 'mongoose';

import { ClientInterface } from '../interfaces/clientInterface';

const clientSchema = new Schema<ClientInterface>(
  {
    origin: { type: String, required: true },
    company: { type: String, required: true },
    productVersion: [{ type: String, required: true }],
    personalInfo: {
      firstName: { type: String, required: true },
      middleName: { type: String },
      lastName: { type: String, required: true },
      email: { type: String, required: true },
      phone: { type: String, required: true },
      authPhoneNumber: { type: String, required: true },
      dateOfBirth: { type: Date, required: true }
    },
    address: {
      street: { type: String, required: true },
      buildingNumber: { type: String, required: true },
      apartmentNumber: { type: String },
      city: { type: String, required: true },
      stateProvince: { type: String, required: true },
      zipCode: { type: String, required: true },
      country: { type: String, required: true }
    },
    operationStatus: { type: String, required: true },
    idDocument: {
      customerIdType: { type: String, required: true },
      number: { type: String, required: true },
      issueDate: { type: Date, required: true },
      expiryDate: { type: Date, required: true },
      issuingCountry: { type: String, required: true },
      idAuthority: { type: String, required: true }
    },
    taxInfo: {
      country: { type: String, required: true },
      taxIdNumber: { type: String, required: true }
    },
    clientID: { type: String, required: true, unique: true },
    cardTypes: [{ type: String }],
    cardCurrency: [{ type: String }],
    client: { type: Boolean, required: true },
    legalId: { type: String, required: true },
    citizenship: { type: String, required: true },
    applicationId: { type: String, required: true },
    riskLevel: { type: Number, required: true },
    riskStatus: { type: String, required: true },
    applicationStatus: { type: String, required: true },
    applicationDate: { type: String, required: true },
    dashboardStatus: { type: String, required: true }
  },
  { timestamps: true }
);

const Client = mongoose.model<ClientInterface>('Client', clientSchema);

export default Client;
