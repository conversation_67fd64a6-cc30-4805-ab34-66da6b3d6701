import { Schema, model } from 'mongoose';
import { CardFee, FXRate } from '../interfaces/feeTypes';
import {
  CardFeeLocation,
  CardFeeType,
  CustomerType,
  CardFeeCurrency,
  FXRatesCurrency,
  OperationType,
  ProductType
} from '../constants/fees';

/**
 * Card Fee Schema
 */
const CardFeeSchema = new Schema<CardFee>(
  {
    GLAccountName: { type: String },
    feeCode: { type: String, required: true, index: true }, // Tx type
    operationType: {
      type: String,
      enum: Object.values(OperationType),
      required: true,
      index: true
    },
    customerType: {
      type: String,
      enum: Object.values(CustomerType),
      required: true,
      index: true
    },
    productType: {
      type: String,
      enum: Object.values(ProductType),
      required: true,
      index: true
    },
    location: {
      type: String,
      enum: Object.values(CardFeeLocation),
      sparse: true,
      index: true
    },
    modifier: { type: String, sparse: true, index: true },
    trigger: { type: String, required: true },
    loopOnCard: { type: Boolean, default: false },
    description: { type: String },
    displayDescriptor: { type: String },
    criteria: { type: String },
    type: {
      type: String,
      enum: Object.values(CardFeeType),
      required: true
    },
    currency: {
      type: String,
      enum: Object.values(CardFeeCurrency),
      required: true
    },
    fixedFee: { type: Number, required: true },
    percentageFee: { type: Number, default: 0 },
    supportsFXFee: { type: Boolean, default: false }
  },
  {
    timestamps: true
  }
);

// Add compound unique index
CardFeeSchema.index({ operationType: 1, customerType: 1, productType: 1, location: 1, modifier: 1 }, { unique: true });

export const CardFeeModel = model<CardFee>('CardFee', CardFeeSchema);

/**
 * FX Rate Schema
 */
const FXRateSchema = new Schema<FXRate>(
  {
    flatFee: { type: Number, required: true },
    percentage: { type: Number, required: true },
    currency: {
      type: String,
      enum: Object.values(FXRatesCurrency),
      required: true
    },
    clientID: { type: String, required: true, index: true }
  },
  {
    timestamps: true
  }
);

// Add compound unique index
FXRateSchema.index({ currency: 1, clientID: 1 }, { unique: true });

export const FXRateModel = model<FXRate>('FXRate', FXRateSchema);
