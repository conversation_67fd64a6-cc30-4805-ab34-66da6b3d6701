import mongoose, { Schema } from 'mongoose';
import { CardInterface } from '../interfaces/debitInterface';

const cardSchema = new Schema<CardInterface>(
  {
    cardHash: { type: String, required: true },
    cardKey: { type: String, unique: true, required: true },
    expDate: { type: String, required: true },
    status: { type: String, required: true },
    statusCode: { type: String, required: true },
    kind: { type: String, required: true },
    productCode: { type: String, required: true },
    productDesc: { type: String, required: true },
    main: { type: Boolean, required: true },
    holder: { type: String, required: true },
    accNo: { type: String, required: true },
    embossName1: { type: String, required: true },
    cardMask: { type: String, required: true }
  },
  { timestamps: true }
);

const Card = mongoose.model<CardInterface>('Card', cardSchema);

export default Card;
