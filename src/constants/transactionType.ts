// Transaction Type Data Interface
export interface TransactionTypeData {
  codeOraSys: string;
  description: string;
  action: 'DR' | 'CR' | 'INQ' | 'ADM' | 'PS' | 'EWT';
  directionType: 'D' | 'C' | 'N';
  ryvylLabel: string;
  ryvylStatement: string;
}

// Types defined by <PERSON><PERSON><PERSON><PERSON>, based on the processing code
export const processingCodeMap: Record<string, TransactionTypeData> = {
  '00': {
    codeOraSys: '1',
    description: 'Goods and Services',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'GDS/SRVS',
    ryvylStatement: 'PURCHASE'
  },
  '01': {
    codeOraSys: '2',
    description: 'Cash Withdrawal',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'CSH WDL',
    ryvylStatement: 'CASH'
  },
  '02': {
    codeOraSys: '29',
    description: 'Debit Adjustment',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'DR ADJ',
    ryvylStatement: 'DEBIT ADJUSTMENT'
  },
  '09': {
    codeOraSys: '1',
    description: 'Goods or services with disbursement',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'G/S DSP',
    ryvylStatement: 'PURCHASE DISBURSEMENT'
  },
  '10': {
    codeOraSys: '12',
    description: 'Non-cash financial instrument e.g. wire transfer',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'MTFR',
    ryvylStatement: 'MONEY TRANSFER'
  },
  '11': {
    codeOraSys: '19',
    description: 'Quasi-cash',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'Q/CSH',
    ryvylStatement: 'QUASI CASH'
  },
  '17': {
    codeOraSys: '22',
    description: 'Funds withdrawal for electronic purse; unlinked unloads to card issuer',
    action: 'DR',
    directionType: 'D',
    ryvylLabel: 'DR WLT',
    ryvylStatement: 'FUNDS WITHDRAWAL'
  },
  '20': {
    codeOraSys: '5',
    description: 'Return (of goods or services)',
    action: 'CR',
    directionType: 'C',
    ryvylLabel: 'RTN GDS/SRVS',
    ryvylStatement: 'PURCHASE RETURN'
  },
  '22': {
    codeOraSys: '28',
    description: 'Credit Adjustment',
    action: 'CR',
    directionType: 'C',
    ryvylLabel: 'CR ADJ',
    ryvylStatement: 'CRED ADJUSTMENT'
  },
  '28': {
    codeOraSys: '25',
    description: 'Funds deposit from e-wallet',
    action: 'CR',
    directionType: 'C',
    ryvylLabel: 'CR WLT',
    ryvylStatement: 'FUNDS CREDIT'
  },
  '29': {
    codeOraSys: '9',
    description: 'Original credit e.g. wire transfer, gaming wins',
    action: 'CR',
    directionType: 'C',
    ryvylLabel: 'CR OCT',
    ryvylStatement: 'ORIGINAL CREDIT TX'
  },
  '30': {
    codeOraSys: '4',
    description: 'Available Funds Inquiry',
    action: 'INQ',
    directionType: 'N',
    ryvylLabel: 'AV/FDS',
    ryvylStatement: 'AVAIL FUNDS'
  },
  '31': {
    codeOraSys: '4',
    description: 'Balance Inquiry',
    action: 'INQ',
    directionType: 'N',
    ryvylLabel: 'BAL',
    ryvylStatement: 'BALANCE INQUIRY'
  },
  '52': {
    codeOraSys: '5',
    description: 'Payment return',
    action: 'PS',
    directionType: 'C',
    ryvylLabel: 'RTN PYMT ',
    ryvylStatement: 'RETURNED PAYMENT'
  },
  '60': {
    codeOraSys: '25',
    description: 'Load value; linked loads ',
    action: 'EWT',
    directionType: 'C',
    ryvylLabel: 'LOAD',
    ryvylStatement: 'LOAD'
  },
  '61': {
    codeOraSys: '22',
    description: 'Unload value; linked unloads ',
    action: 'EWT',
    directionType: 'D',
    ryvylLabel: 'UNLOAD',
    ryvylStatement: 'UNLOAD'
  },
  '70': {
    codeOraSys: '3',
    description: 'PIN Change',
    action: 'ADM',
    directionType: 'D',
    ryvylLabel: 'PIN CHG',
    ryvylStatement: 'PIN CHANGE'
  }
};

// Allowed OraSys codes (unique)
export const allOraSysCodes = Array.from(new Set(Object.values(processingCodeMap).map((entry) => entry.codeOraSys)));

// Get the 1st Object Type from OraSys code
export function findTransactionTypeByOraSysCode(codeOraSys: string): TransactionTypeData | undefined {
  const code = String(Number(codeOraSys)); // format if there are 0s '01' => '1'
  for (const key in processingCodeMap) {
    if (processingCodeMap[key].codeOraSys === code) {
      return processingCodeMap[key];
    }
  }
  return undefined; // Return undefined if no match is found
}

// Mapping cannot work correctly since WLT is used for both debit and credit
export const transactionCodeMap: Record<string, number> = {
  POS1: 1, // POS Payment
  ATM1: 2, // ATM Withdrawal
  PIN: 3, // PIN Change
  ATM4: 4, // ATM Balance Inquiry
  POS2: 5, // Refund (POS)
  POS3: 6, // Reversal (POS)
  ATM2: 7, // Refund (ATM)
  ATM3: 8, // Reversal (ATM)
  OCT1: 9, // Original Credit Transaction (Visa Direct)
  OCT2: 10, // Refund (OCT)
  OCT3: 11, // Reversal (OCT)
  AFT1: 12, // Account Funding Transaction (Visa Direct)
  AFT2: 14, // Refund (AFT)
  AFT3: 15, // Reversal (AFT)
  ECM1: 16, // ECOM Payment (Card Not Present)
  ECM2: 17, // Refund (ECOM)
  ECM3: 18, // Reversal (ECOM)
  UNQ1: 19, // Quasi Cash (UNQ) - Trading/Gambling/Crypto
  UNQ2: 20, // Refund (UNQ)
  UNQ3: 21, // Reversal (UNQ)
  WLT1: 22, // EWallet Debit (Unload from e-wallet to card)
  WLT2: 23, // Refund (WLT)
  WLT3: 24, // Reversal (WLT)
  // WLT1: 25, // EWallet Credit (load to wallet) — Same as 22, mapping will fail
  // WLT2: 26, // EWallet Credit (load to wallet) — Same as 23, mapping will fail
  // WLT3: 27, // EWallet Credit (load to wallet) — Same as 24, mapping will fail
  ADJ1: 28, // Manual Adjustment Credit (ADJ)
  ADJ2: 29, // Manual Adjustment Debit (ADJ)
  ADJ3: 30 // Cancel Adjustment (ADJ)
  // TAX1: 13,  // Tax 1 (legacy entry)
  // TAX2: 14,  // Tax 2 (legacy entry)
  // TAXN: 15   // Tax N (legacy entry)
};
