/**
 * Enum representing the possible operation types
 */
export enum OperationType {
  CARD_ISSUING_FEE = 'Card Issuing Fee',
  CARD_DELIVERY_FEE = 'Card Delivery Fee',
  CARD_REISSUING_FEE = 'Card Reissuing Fee',
  ADDITIONAL_CARD_ISSUING_FEE = 'Additional Card Issuing Fee',
  EXPRESS_CARD_ISSUING_FEE = 'Express Card Issuing Fee',
  MONTHLY_CARD_MAINTENANCE_FEE = 'Monthly Card Maintenance Fee',
  CARD_ACTIVATION_FEE = 'Card Activation Fee',
  CARD_DEACTIVATION_FEE = 'Card Deactivation Fee',
  CARD_BLOCK_FEE = 'Card Block Fee',
  CARD_UNBLOCK_FEE = 'Card Unblock Fee',
  THREE_DS_SMS_FEE = '3DS SMS fee',
  // Transaction Operations
  POS_PAYMENT_FEE = 'POS Payment',
  ATM_WITHDRAWAL_FEE = 'ATM Withdrawal',
  GOODS_AND_SERVICES = 'Goods and Services',
  CASH_WITHDRAWAL = 'Cash Withdrawal',
  DEBIT_ADJUSTMENT = 'Debit Adjustment',
  GOODS_AND_SERVICES_WITH_DISBURSEMENT = 'Goods or services with disbursement',
  NON_CASH_FINANCIAL_INSTRUMENT = 'Non-cash financial instrument e.g. wire transfer',
  QUASI_CASH = 'Quasi-cash',
  ELECTRONIC_PURSE_UNLINKED_UNLOAD = 'Funds withdrawal for electronic purse; unlinked unloads to card issuer',
  RETURN_OF_GOODS_OR_SERVICES = 'Return (of goods or services)',
  CREDIT_ADJUSTMENT = 'Credit Adjustment',
  FUNDS_DEPOSIT_FROM_EWALLET = 'Funds deposit from e-wallet',
  ORIGINAL_CREDIT = 'Original credit e.g. wire transfer, gaming wins',
  PAYMENT_RETURN = 'Payment return',
  LOAD_VALUE_LINKED = 'Load value; linked loads',
  UNLOAD_VALUE_LINKED = 'Unload value; linked unloads',
  PIN_CHANGE = 'PIN Change'
}

/**
 * Enum representing the possible types of customers
 */
export enum CustomerType {
  CONSUMER = 'Consumer',
  BUSINESS = 'Business',
  INDIVIDUAL = 'Individual',
  RYVYL_CONSUMER = 'RYVYL_Consumer',
  RYVYL_INDIVIDUAL = 'RYVYL_Individual',
  NO_FEE_RYVYL_EMPLOYEE = 'No-Fee_RYVYL_Employee'
}

/**
 * Enum representing the possible types of products
 */
export enum ProductType {
  DEBIT_PHYSICAL_CARD = 'Debit Physical Card',
  DEBIT_VIRTUAL_CARD = 'Debit Virtual Card',
  PREPAID_PHYSICAL_CARD = 'Prepaid Physical Card',
  PREPAID_VIRTUAL_CARD = 'Prepaid Virtual Card',
  R001 = 'R001', // RYVYL VISA CONSUMER DEBIT PHY
  R002 = 'R002', // RYVYL VISA CONSUMER DEBIT VTL
  R003 = 'R003', // RYVYL VISA BUSINESS DEBIT PHY
  R004 = 'R004' // RYVYL VISA BUSINESS DEBIT VTL
}

/**
 * Enum representing the possible types of card fees
 */
export enum CardFeeType {
  TRANSACTIONAL = 'Transactional',
  NON_TRANSACTIONAL = 'Non-Transactional',
  MANUAL = 'Manual'
}

/**
 * Enum representing the possible locations for card fees
 */
export enum CardFeeLocation {
  BG = 'BG',
  GB = 'GB',
  PL = 'PL'
}

/**
 * Enum representing the possible triggers for card fees
 */
export enum CardFeeTrigger {
  API_CALL = 'API Call',
  TRANSACTION_MESSAGE = 'Transaction Message'
}

/**
 * Enum representing the possible currencies for fees
 */
export enum CardFeeCurrency {
  EUR = 'EUR'
}

/**
 * Enum representing the possible currencies for FX rates to EUR
 */
export enum FXRatesCurrency {
  USD = 'USD',
  CNY = 'CNY',
  JPY = 'JPY',
  GBP = 'GBP',
  INR = 'INR',
  CHF = 'CHF',
  CAD = 'CAD',
  AUD = 'AUD',
  KRW = 'KRW',
  HKD = 'HKD',
  SGD = 'SGD',
  SEK = 'SEK',
  NOK = 'NOK',
  MXN = 'MXN',
  BRL = 'BRL',
  ZAR = 'ZAR',
  TRY = 'TRY',
  THB = 'THB',
  NZD = 'NZD'
}

/*
 * Map from 3 letter country code to 2 letter code
 */
export const threeToTwoLetterCountryMap: Record<string, CardFeeLocation> = {
  BUL: CardFeeLocation.BG, // Bulgaria
  GBR: CardFeeLocation.GB, // United Kingdom
  POL: CardFeeLocation.PL // Poland
};
