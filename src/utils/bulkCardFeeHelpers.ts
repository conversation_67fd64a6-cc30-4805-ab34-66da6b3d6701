import { Request, Response } from 'express';
import fs from 'fs/promises';

import { parseExcelToCardFees } from '../services/excelParserService';
import { cardFeeLogger } from '../utils/logger';
import { getErrorMessage, ServiceResult } from '../utils/helpers';
import { CardFee } from '../interfaces/feeTypes';

const feeControllerLogger = cardFeeLogger.child({ controller: 'CardFeeController' });

export function validateBulkRequest(req: Request): { isValid: boolean; error?: string } {
  const hasFile = req.files && (req.files as Express.Multer.File[]).length > 0;
  const hasJsonData = req.body.cardFees && (Array.isArray(req.body.cardFees) || req.body.cardFees);
  if (hasFile && hasJsonData) {
    return {
      isValid: false,
      error: 'Only one data source allowed. Please provide either a file OR cardFees array, not both.'
    };
  }

  if (!hasFile && !hasJsonData) {
    return { isValid: false, error: 'No data provided. Please provide either a file OR cardFees array.' };
  }

  return { isValid: true };
}

export async function processBulkData(
  req: Request
): Promise<
  ServiceResult<{ validRecords: CardFee[]; invalidRecords: any[] }, { error: string; invalidRecords?: any[] }>
> {
  const hasFile = req.files && (req.files as Express.Multer.File[]).length > 0;
  if (hasFile) {
    return await processFileUpload(req);
  } else {
    return await processJsonData(req);
  }
}

/**
 * Processes a file upload request. The supported files are .xls, .xlsx, .csv, and any alternative CSV mime type.
 * @param req - Express request object containing the file to be processed.
 * @returns An object containing the success status, data with the valid and invalid records, and error, if any.
 */
export async function processFileUpload(
  req: Request
): Promise<
  ServiceResult<
    { validRecords: CardFee[]; invalidRecords: any[] },
    { error: string; details?: string; invalidRecords?: any[] }
  >
> {
  const files = req.files as Express.Multer.File[];
  const file = files[0];

  try {
    const parseResult = await parseExcelToCardFees(file.path);

    // Clean up file immediately after reading
    await fs.unlink(file.path);

    if (parseResult.validRecords.length === 0) {
      return {
        success: false,
        error: {
          error: 'No valid records found in file',
          invalidRecords: parseResult.invalidRecords
        }
      };
    }

    return {
      success: true,
      data: {
        validRecords: parseResult.validRecords,
        invalidRecords: parseResult.invalidRecords
      }
    };
  } catch (parseError) {
    // Clean up file on error
    try {
      await fs.unlink(file.path);
    } catch (unlinkError) {
      feeControllerLogger.warn(`Failed to delete temp file: ${unlinkError}`);
    }

    const errDetails = getErrorMessage(parseError);
    feeControllerLogger.error(`Failed to parse Excel file: ${errDetails}`);

    return {
      success: false,
      error: { error: 'Failed to parse Excel file', details: errDetails }
    };
  }
}

export async function processJsonData(
  req: Request
): Promise<ServiceResult<{ validRecords: CardFee[]; invalidRecords: any[] }, { error: string }>> {
  const cardFees = Array.isArray(req.body.cardFees) ? req.body.cardFees : [];

  if (cardFees.length === 0) {
    return {
      success: false,
      error: { error: 'No card fees provided in request body' }
    };
  }

  return {
    success: true,
    data: {
      validRecords: cardFees,
      invalidRecords: []
    }
  };
}

export function sendBulkResponse(
  res: Response,
  creationResult: { created: any[]; errors: string[] },
  invalidRecords: any[]
): void {
  const response: any = {
    created: creationResult.created.length,
    errors: creationResult.errors.length + invalidRecords.length,
    createdFees: creationResult.created,
    errorDetails: creationResult.errors
  };

  if (invalidRecords.length > 0) {
    response.invalidRecords = invalidRecords;
  }

  // Return appropriate status code
  if (creationResult.created.length !== 0 && creationResult.errors.length === 0) {
    res.status(201).json(response);
  } else if (creationResult.created.length === 0 && creationResult.errors.length !== 0) {
    res.status(400).json(response);
  } else {
    res.status(206).json(response);
  }
}
