import express from 'express';
import logger from '../utils/logger';

export interface ExtendedRequest extends express.Request {
  rawData?: Buffer;
}

const parseBody = (req: ExtendedRequest, _res: express.Response, next: express.NextFunction) => {
  const contentType = req.headers['content-type'] || '';

  // ✅ Skip parsing for file uploads
  if (contentType.includes('multipart/form-data')) {
    return next();
  }

  let rawData = '';

  req.on('data', (chunk) => {
    rawData += chunk;
  });

  req.on('end', () => {
    try {
      if (rawData) {
        req.rawData = Buffer.from(rawData);
        req.body = JSON.parse(rawData);
      } else {
        req.rawData = Buffer.alloc(0);
        req.body = null;
      }
    } catch (error) {
      logger.error('Error parsing JSON body: ', error);
      req.body = null;
    } finally {
      next();
    }
  });

  req.on('error', (err) => {
    logger.error('Stream error while parsing body:', err);
    next(); // proceed even on stream error
  });
};

export default parseBody;
