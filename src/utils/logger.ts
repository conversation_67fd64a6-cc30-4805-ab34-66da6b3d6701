import logger from '@submodules/ryvyl-commons/services/loggerService';
import { createLoggerInstance, AIClassifierLogger } from '@submodules/ryvyl-commons/services/loggerService';

// Create loggers using the shared transports
export { AIClassifierLogger };
export const ATMLogger = createLoggerInstance('ATM');
export const CIPLogger = createLoggerInstance('CIP');
export const fileLogger = createLoggerInstance('File');
export const sftpLogger = createLoggerInstance('SFTP');
export const cronLogger = createLoggerInstance('Cron');
export const oneDriveLogger = createLoggerInstance('OneDrive');
export const settlementCronLogger = createLoggerInstance('SettlementCron');
export const resolveCronLogger = createLoggerInstance('ResolveCron');
export const partnerLogger = createLoggerInstance('Partner');
export const settlementLogger = createLoggerInstance('Settlement');
export const oracleDBLogger = createLoggerInstance('oracleDB');
export const heartbeatLogger = createLoggerInstance('Heartbeat');
export const requestLogger = createLoggerInstance('Request');
export const adviceLogger = createLoggerInstance('Advice');
export const reversalLogger = createLoggerInstance('Reversal');
export const transactionLogger = createLoggerInstance('Transaction');
export const cardFeeLogger = createLoggerInstance('CardFee');

logger.info('Logger extended attached.');

export default logger;
