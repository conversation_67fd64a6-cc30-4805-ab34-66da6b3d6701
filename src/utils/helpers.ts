import axios, { AxiosRequestConfig, Method } from 'axios';

import { currencyDecimalMap, currencyMap } from '../constants/currency';

// ____________________ Back-end Request Handler ____________________
interface RequestOptions {
  method: Method;
  url: string;
  maxBodyLength?: any;
  params?: Record<string, any>; // For query string
  data?: Record<string, any>; // For request body
  headers?: Record<string, string>;
  timeout?: number;
}

export const makeBackendRequest = async (options: RequestOptions) => {
  const { method, url, maxBodyLength, params, data, headers, timeout = 10000 } = options;

  const config: AxiosRequestConfig = {
    method,
    url,
    maxBodyLength,
    params,
    data,
    headers,
    timeout
  };

  try {
    const response = await axios(config);
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error: any) {
    return {
      success: false,
      status: error.response?.status || 500,
      message: error.message,
      data: error.response?.data || null
    };
  }
};

// ____________________ Extract Fields ____________________

// Interface for field extraction options
interface FieldExtractionOptions {
  fields: string[];
  includeAll?: boolean;
}

/**
 * Generic function to extract specified fields from ISO8583 data
 * @param data The original ISO8583 fields data
 * @param options Field extraction options
 * @returns An object containing only the specified fields
 */
export function extractFields<T extends Record<string, any>>(data: T, options: FieldExtractionOptions): Partial<T> {
  const { fields, includeAll = false } = options;
  const result: Partial<T> = {};

  if (includeAll) return { ...data };

  // Add only the specified fields to the result
  fields.forEach((field) => {
    if (field in data) {
      // As any is safe in this context because it already checked if the field exists
      (result as any)[field] = data[field];
    }
  });

  return result;
}

// ____________________ Formatters ____________________

/**
 * Formats a date string in ISO format to a string in the format DD.MM.YYYY HH:MM
 * @param isoString - The date string in ISO format
 * @returns The formatted date string
 */
export function formatDateTime(isoString: string): string {
  const date = new Date(isoString);
  const pad = (n: number) => n.toString().padStart(2, '0');

  // Extract UTC components to avoid timezone issues
  const day = pad(date.getUTCDate());
  const month = pad(date.getUTCMonth() + 1);
  const year = date.getUTCFullYear();
  const hours = pad(date.getUTCHours());
  const minutes = pad(date.getUTCMinutes());

  return `${day}.${month}.${year} ${hours}:${minutes}`;
}

/**
 * Converts YYMMDDhhmmss date format to ISO 8601 format
 * @param dateStr The date string in YYMMDDhhmmss format (e.g., "240622120639")
 * @returns ISO 8601 formatted date string or null if invalid input
 */
export function convertYYMMDDhhmmssToISO(dateStr: string | undefined): string | null {
  if (!dateStr) return null;
  // Check if the input string matches the expected format
  if (!/^\d{12}$/.test(dateStr)) {
    return null;
  }

  // Extract date components
  const year = '20' + dateStr.substring(0, 2); // Assuming years 2000-2099
  const month = dateStr.substring(2, 4);
  const day = dateStr.substring(4, 6);
  const hour = dateStr.substring(6, 8);
  const minute = dateStr.substring(8, 10);
  const second = dateStr.substring(10, 12);

  try {
    // Create ISO string directly without using Date object to avoid timezone issues
    return `${year}-${month}-${day}T${hour}:${minute}:${second}.000Z`;
  } catch (error) {
    return null;
  }
}

/**
 * Converts ISO 8601 date format to YYMMDDhhmmss format
 * @param isoDateStr The date string in ISO 8601 format
 * @returns The formatted date string (YYMMDDhhmmss) or null if invalid input
 */
export function convertISOToYYMMDDhhmmss(isoDateStr: string | undefined): string | null {
  if (!isoDateStr) return null;

  try {
    const date = new Date(isoDateStr);

    if (isNaN(date.getTime())) {
      return null;
    }

    // Format components with leading zeros using UTC methods
    const year = String(date.getUTCFullYear()).slice(-2); // Get last 2 digits of year
    const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // +1 because months are 0-based
    const day = String(date.getUTCDate()).padStart(2, '0');
    const hours = String(date.getUTCHours()).padStart(2, '0');
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');
    const seconds = String(date.getUTCSeconds()).padStart(2, '0');

    // Combine in YYMMDDhhmmss format
    return `${year}${month}${day}${hours}${minutes}${seconds}`;
  } catch (error) {
    return null;
  }
}

/**
 * Returns the number of decimal places for a given currency code
 * @param currency The currency code
 * @returns The number of decimal places
 */
export function getDecimals(currency: string): number {
  return currencyDecimalMap[currency] ?? 2;
}

/**
 * Formats an amount to a string with the correct number of decimal places
 * @param value The amount to format
 * @param currencyCode The currency code
 * @returns The formatted amount
 */
export function formatAmount(value: number, currencyCode: string): string {
  const decimals = getDecimals(currencyCode);
  const scaled = BigInt(value.toFixed(decimals).replace('.', ''));
  const maxAllowed = BigInt('999999999999');

  const safeScaled = scaled > maxAllowed ? maxAllowed : scaled;
  return safeScaled.toString().padStart(12, '0');
}

/**
 * Returns the currency code for a given ITCard code
 * @param code The ITCard code
 * @returns The currency code
 */
export const getCurrency = (code: string | undefined): string | undefined => {
  if (!code) return;

  return currencyMap[code] || code; // Return the code if not found in the map
};

/**
 * Normalizes an input that could be a single object or an array of objects to always be an array
 * @param input The input to normalize
 * @returns An array of objects
 */
export function normalizeToArray<T>(input: T | T[] | undefined): T[] {
  if (!input) return [];
  return Array.isArray(input) ? input : [input];
}

/**
 * Safely extracts error message from unknown error types
 * @param error - The error object of unknown type
 * @returns The error message as string
 */
export function getErrorMessage(error: unknown): string {
  if (!error) return 'Unknown error';

  if (error instanceof Error) {
    return error.message;
  }

  return error as string;
}

/**
 * Cleans a string value by trimming spaces and normalizing whitespace
 * @param value - The value to clean
 * @returns Cleaned string or original value if not a string
 */
export function cleanStringValue(value: any): any {
  if (typeof value === 'string') {
    // Trim spaces and replace multiple spaces with single space
    return value.trim().replace(/\s+/g, ' ');
  }

  return value;
}

/**
 * Cleans a string value and checks if it's a valid non-empty value (not N/A variations)
 * @param value - The value to clean and validate
 * @returns True if the value is valid and not empty, false otherwise
 */
export function isValidStringValue(value: any): boolean {
  if (typeof value !== 'string') return false;

  const cleaned = cleanStringValue(value);
  if (!cleaned || cleaned === '') return false;

  // Check for N/A variations (case-insensitive)
  const naVariations = ['N/A', 'n/a', 'na', 'n.a', 'n.a.', 'not applicable', 'not available'];
  return !naVariations.includes(cleaned.toLowerCase());
}

/**
 * Returns the last 3 letters of a string
 * @param input The string to extract from
 * @returns The last 3 letters
 */
export function getLast3Letters(input: string): string {
  return input.slice(-3);
}

/**
 * Returns the current date in UTC as a string in ISO format
 * @returns The current date in UTC as a string
 */
export function convertDateToISO_UTC(date: Date): string {
  return date.toISOString();
}

/**
 * Returns the current date in UTC as a string in ISO format
 * @returns The current date in UTC as a string
 */
export function getCurrentUTCDateISO(): string {
  return convertDateToISO_UTC(new Date());
}

/**
 * Type to represent success with data or error
 * @typeparam T The type of the data on success
 * @typeparam E The type of the error on failure, defaults to string
 */
export type ServiceResult<T, E = string> = { success: true; data: T } | { success: false; error: E };
