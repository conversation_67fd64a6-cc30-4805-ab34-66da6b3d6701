{"compilerOptions": {"module": "commonjs", "target": "es6", "allowJs": true, "outDir": "./dist", "rootDir": "./", "baseUrl": ".", "paths": {"@submodules/*": ["submodules/*"]}, "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true}, "include": ["src/**/*", "submodules/ryvyl-commons/types/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}