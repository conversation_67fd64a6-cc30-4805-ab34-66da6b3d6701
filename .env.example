ENV=<DEV/STAGING/PROD>
PORT=<desired_port>
PARTNERS_PORT=<desired_partners_port>
RYVYL_ORA_AUTH_URL=<ryvyl_ora_auth_url>
RYVYL_EMAIL_SERVICE_URL=<ryvyl_email_service_url>

# Logger
LOG_LEVEL=<If we want some specific level>
LOGGER_UPLOAD_LOGS=<true or false>
LOGGER_SUBDOMAIN=<Subdomain for the logger>
LOGGER_TOKEN=<Token for the logger>
LOGGER_TAGS=<Loggly tags separated by comma (,)>

# Mongoose
MONGODB_HOST=<your_mongodb_host>
MONGODB_PORT=<port_that_mongodb_runs>
MONGODB_DATABASE_NAME=<mongodb_database_name>
MONGODB_USERNAME=<mongodb_username>
MONGODB_PASSWORD=<mongodb_user_password>
MONGODB_URI=<mongodb_uri>
CA_CERT=<ca_cert>

# JWT
JWT_PUBLIC_KEY=<your_public_key>
JWT_SECRET_KEY=<your_private_key>
JWT_ALGORITHM="ES512"
JWT_EXPIRES_IN=<expiration_period_in_days> // for example 1d

# OpenAI
OPENAI_API_KEY=<your_open_ai_key>
OPENAI_API_ORGANIZATION=<your_open_ai_organization>
OPENAI_API_PROJECT=<your_open_ai_project>

# Kafka
KAFKA_BROKER_URL=<your_kafka_broker_url>
KAFKA_GROUP_ID=<your_kafka_group_id>

# Oracle DB
ORACLE_USER=<oracle_db_user>
ORACLE_PASSWORD=<oracle_db_password>
ORACLE_CONNECTING_STRING=<oracle_db_connecting>

# Cron job
START_CRON_JOB=<true or false>
DAYS_TO_CHECK_BACK=<number_of_days_to_check_back>

# Whitelisting
IP_WHITELIST=<main IP list for verification for regular endpoints>
IP_WHITELIST_ITCARD=<comma-separated IP list for IT Card>
IP_WHITELIST_CIP=<comma-separated IP list for CIP>

# SFTP
SFTP_HOST_IT_CARD=<sftp_host_it_card>
SFTP_PORT_IT_CARD=<sftp_port_it_card>
SFTP_USER_IT_CARD=<sftp_user_it_card>
SFTP_PRIVATE_KEY_IT_CARD=<sftp_private_key_it_card>

SFTP_HOST_DROPBOX=<sftp_host_dropbox>
SFTP_PORT_DROPBOX=<sftp_port_dropbox>
SFTP_USER_DROPBOX=<sftp_user_dropbox>
SFTP_PRIVATE_KEY_DROPBOX=<sftp_private_key_dropbox>

# Start from BlockID - used for staging
START_FROM_BLOCK_ID=<block_id_to_start_from>

# OneDrive Integration
ONEDRIVE_TENANT_ID=<Your Azure AD Tenant ID>
ONEDRIVE_CLIENT_ID=<Your Azure AD App Client ID>
ONEDRIVE_CLIENT_SECRET=<Your Azure AD App Client Secret>
ONEDRIVE_EMAIL=<The Email of the user that has the files>