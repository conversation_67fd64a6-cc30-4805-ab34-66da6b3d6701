FROM oraclelinux:8

RUN dnf module enable nodejs:20
RUN dnf install -y nodejs

RUN dnf install oracle-release-el8
RUN dnf install oracle-instantclient19.10-basic

ENV PORT=
ENV PARTNERS_PORT=

ENV MONGODB_HOST=
ENV MONGODB_PORT=
ENV MONGODB_DATABASE_NAME=
ENV MONGODB_USERNAME=
ENV MONGODB_PASSWORD=
ENV MONGODB_URI=

ENV ORACLE_USER=
ENV ORACLE_PASSWORD=
ENV ORACLE_CONNECTING_STRING=

ENV LOG_LEVEL=
ENV LOGGER_UPLOAD_LOGS=
ENV LOGGER_SUBDOMAIN=
ENV LOGGER_TAGS=
ENV LOGGLY_TOKEN=

WORKDIR /ryvyl-card-processing
COPY . .

# Install dependencies
RUN npm install
RUN npm run replace-paths && npm run build
RUN npm run prestart

EXPOSE 22255 22566

CMD ["npm", "run", "prod"]