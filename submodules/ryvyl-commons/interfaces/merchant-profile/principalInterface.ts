import { PrincipalType } from '../../../ryvyl-commons/types/merchant-profile/principalType';
import { SumsubData } from '../../../ryvyl-commons/interfaces/merchant-profile/acquiringEntityInterface';

import { ObjectId } from 'mongoose';

export interface Principal {
  firstName: string;
  middleName?: string;
  lastName: string;
  email?: string;
  phone?: string;
  // Country to be full name NOT 2 or 3 letters code
  country?: string;
  state?: string;
  city?: string;
  address1?: string;
  address2?: string;
  zip?: string;
  dateOfBirth?: string;
  passportNumber?: string;
  driverLicenseNumber?: string;
  positionInCompany?: PrincipalType;
  acquiringEntityId?: ObjectId;
  sumsub?: SumsubData;
  connectBeneficiaryToCompanyError?: any;
  isUBODirector?: boolean; // This fields is used in a dicrector object
  isControllerDirector?: boolean;
  aditionalData?: PrincipalAditionalData;
}

export interface PrincipalAditionalData {
  isSumsubKycEmailSent?: boolean;
}
