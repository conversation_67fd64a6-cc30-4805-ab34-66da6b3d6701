
export interface TraceRequest {
  clientId: string | number;
  ipAddress: string;
  endpointURL: string;
  method: string;
  responseStatusCode?: number;
  dataReceived?: any;
  dataSend?: any;
  userAgent?: string; // Identify the browser or application making the request
  type?: string; // Can add type of the request, like (LOGIN = 'LOGIN', CREATE_INDIVIDUAL = 'CREATE_INDIVIDUAL' and etc. )
  errorLogSendToTheClient?: any;
  errorLogInitial?: any;
}
