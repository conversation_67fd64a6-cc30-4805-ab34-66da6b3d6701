export class AppError extends Error {
  public statusCode?: number;
  public userMessage: string;
  public agentMessage?: string;

  constructor({
    userMessage,
    agentMessage,
    statusCode = 500
  }: {
    userMessage: string;
    agentMessage?: string;
    statusCode?: number;
  }) {
    super(agentMessage ?? userMessage); // The "real" error shown in logs, etc.
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.userMessage = userMessage;
    this.agentMessage = agentMessage;

    // Required for instanceof checks to work properly when transpiled
    // Because without that line, it might be false.
    // example: console.log(err instanceof AppError); // ✅ true (because of that line)
    Object.setPrototypeOf(this, AppError.prototype);
  }
}
