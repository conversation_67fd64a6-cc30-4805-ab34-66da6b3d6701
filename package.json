{"name": "card-processing", "version": "1.0.0", "main": "index.js", "_moduleAliases": {"@submodules": "./submodules"}, "scripts": {"update-ryvyl-commons": "git submodule update --init --recursive --remote submodules/ryvyl-commons", "preinstall": "node checkNodeVersion", "prestart": "node checkNodeVersion", "start": "ts-node-dev --respawn --transpile-only --exit-child --clear ./src/index.ts", "replace-paths": "node submodules/ryvyl-commons/scripts/replace-paths.js ./src/", "build": "tsc --sourcemap", "prod": "node ./dist/src/index.js", "format": "pretty-quick --staged", "lint": "eslint . --ext ts --ext js --fix", "prepare": "husky", "test": "jest --verbose --setupFiles dotenv/config"}, "keywords": [], "author": "Encorp.io", "license": "ISC", "description": "", "devDependencies": {"@types/cors": "^2.8.17", "@types/k6": "^1.0.2", "@types/multer": "^1.4.12", "@types/node": "^22.13.10", "@types/node-cron": "^3.0.11", "@types/oracledb": "^6.5.3", "@types/ssh2-sftp-client": "^9.0.4", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "5.3.1", "@typescript-eslint/parser": "5.3.1", "eslint": "8.2.0", "eslint-config-google": "0.14.0", "eslint-plugin-jsdoc": "37.0.3", "nodemon": "^3.1.9", "prettier": "^3.5.3", "pretty-quick": "^4.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}, "dependencies": {"common": "file:submodules/ryvyl-commons", "cors": "^2.8.5", "fast-xml-parser": "^5.2.1", "helmet": "^8.0.0", "http": "^0.0.1-security", "module-alias": "^2.2.3", "multer": "^1.4.5-lts.2", "oracledb": "^6.8.0", "ssh2-sftp-client": "^12.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz"}, "overrides": {"node-loggly-bulk": {"axios": "^1.8.2"}}}